import { HasChildrenTrait } from "@figma/rest-api-spec";
import type { FigmaNode } from "./types";

export type PerformUnitOfWorkResult<R> = {
    /**
     * 每个节点的处理结果
     */
    result: R;
    /**
     * 是否继续遍历子节点
     */
    shouldTraverseChildren: boolean;
};

/**
 * 每个节点的处理函数
 */
export type PerformUnitOfWork<R> = (node: FigmaNode, parentOrInitWorkResult: R, parent: FigmaNode | undefined) => PerformUnitOfWorkResult<R>;

/**
 * 每个节点的处理函数
 */
export type PerformUnitOfWorkAsync<R> = (
    node: FigmaNode,
    parentOrInitWorkResult: R,
    parent: FigmaNode | undefined,
    childIndex?: number,
) => Promise<PerformUnitOfWorkResult<R>>;

/**
 * 调优参数
 */
export type PerformanceParams = {
    /**
     * 分批遍历，每批的个数，默认 500
     * 小于 1 表示不分批 
     */
    batchSize?: number;
    /**
     * 分批遍历，每批之间的间隔休息毫秒数，默认 0
     */
    sleepTime?: number;
};

const DEFAULT_PERFORMANCE_PARAMS = {
    batchSize: 500,
    sleepTime: 0,
};

/**
 * 通用的 Figma 节点遍历算法
 * > 非递归前序遍历
 * > 可通过配置参数实现分批遍历、每批之间通过 setTimeout 让主 JS 线程执行更高优先级任务，避免阻塞
 * @param root 根节点
 * @param performUnitOfWork 每个节点的处理函数
 * @param initWorkResult 处理结果第一个默认值
 * @param performanceParams 调优参数
 * @returns 根节点的处理结果
 */
export async function figmaNodeTraverse<R>(
    root: FigmaNode,
    performUnitOfWork: PerformUnitOfWork<R>,
    initWorkResult: R,
    performanceParams?: PerformanceParams,
) {
    let rootWorkResult: R | undefined;
    const performance = {
        ...DEFAULT_PERFORMANCE_PARAMS,
        ...performanceParams
    };
    const stack: {
        node: FigmaNode,
        parent: FigmaNode | undefined,
        parentOrInitWorkResult: R,
    } [] = [];
    let loopCounter = 0;
    
    if(root) {
        stack.push({ node: root, parentOrInitWorkResult: initWorkResult, parent: undefined });

        while (stack.length > 0) {
            if(performance.batchSize > 0
               && loopCounter % performance.batchSize === 0) {
               await sleep(performance.sleepTime);
            }
            const current = stack.pop()!;
            const workResult = performUnitOfWork(current.node, current.parentOrInitWorkResult, current.parent);
            if(typeof rootWorkResult === 'undefined') {
                rootWorkResult = workResult.result;
            }

            if (hasChildren(current.node as HasChildrenTrait) && workResult.shouldTraverseChildren) {
              for (let i = (current.node as HasChildrenTrait).children.length - 1; i >= 0; i--) {
                stack.push({ node: (current.node as HasChildrenTrait).children[i], parentOrInitWorkResult: workResult.result, parent: current.node });
              }
            }
            
            loopCounter ++;
        }
    }
    return rootWorkResult;
}

/**
 * 通用的 Figma 节点遍历算法
 * > 非递归前序遍历
 * > 可通过配置参数实现分批遍历、每批之间通过 setTimeout 让主 JS 线程执行更高优先级任务，避免阻塞
 * @param root 根节点
 * @param performUnitOfWork 每个节点的处理函数
 * @param initWorkResult 处理结果第一个默认值
 * @param performanceParams 调优参数
 * @returns 根节点的处理结果
 */
export async function figmaNodeTraverseAsync<R>(
    root: FigmaNode,
    performUnitOfWork: PerformUnitOfWorkAsync<R>,
    initWorkResult: R,
    performanceParams?: PerformanceParams,
) {
    let rootWorkResult: R | undefined;
    const performance = {
        ...DEFAULT_PERFORMANCE_PARAMS,
        ...performanceParams
    };
    const stack: {
        node: FigmaNode,
        parent: FigmaNode | undefined,
        parentOrInitWorkResult: R,
        childIndex: number,
    } [] = [];
    let loopCounter = 0;

    if(root) {
        stack.push({
            node: root,
            parentOrInitWorkResult: initWorkResult,
            parent: undefined,
            childIndex: 0,
        });

        while (stack.length > 0) {
            if(performance.batchSize > 0
                && loopCounter % performance.batchSize === 0) {
                await sleep(performance.sleepTime);
            }
            const current = stack.pop()!;
            const workResult = await performUnitOfWork(
                current.node,
                current.parentOrInitWorkResult,
                current.parent,
                current.childIndex,
            );

            if(typeof rootWorkResult === 'undefined') {
                rootWorkResult = workResult.result;
            }

            if (hasChildren(current.node as HasChildrenTrait) && workResult.shouldTraverseChildren) {
                for (let i = (current.node as HasChildrenTrait).children.length - 1; i >= 0; i--) {
                    stack.push({
                        node: (current.node as HasChildrenTrait).children[i] as FigmaNode,
                        parentOrInitWorkResult: workResult.result,
                        parent: current.node,
                        childIndex: i,
                    });
                }
            }

            loopCounter ++;
        }
    }
    return rootWorkResult;
}

function hasChildren(node: HasChildrenTrait): boolean  {
    return node.children?.length > 0;
}
  

async function sleep(time: number) {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(true);
        }, time);
    });
}