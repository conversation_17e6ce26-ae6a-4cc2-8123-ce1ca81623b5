import { FrameNode, RectangleNode, TextNode, Paint, Rectangle, TypeStyle } from '@figma/rest-api-spec';
import type { NodeDefinition, NodeStyle, Rectangle as AltRectangle, FigmaNode } from '../types';
import {
  getShadow,
  buildColor,
  buildSizeStringByUnit,
  keepDecimal,
  notEmptyNumber,
  getRawLeftTopCoordinate,
  getCSSShorthand,
  transformGradient
} from '../utils';

interface GridCell {
  start: number[];
  end: number[];
}

type UnitType = 'px' | 'rem';

/** Start Units */

const gridJustifyContentCssValues = {
  MIN: 'start',
  MAX: 'end',
  CENTER: 'center',
  STRETCH: 'stretch'
};

const justifyContentCssValues = {
  MIN: 'flex-start',
  MAX: 'flex-end',
  CENTER: 'center',
  SPACE_BETWEEN: 'space-between'
};

const alignItemsCssValues = {
  MIN: 'flex-start',
  MAX: 'flex-end',
  CENTER: 'center',
  BASELINE: 'baseline'
};

const textAlignCssValues = {
  LEFT: 'left',
  RIGHT: 'right',
  CENTER: 'center',
  JUSTIFIED: 'justify'
};

const textAlignCssFlexValues = {
  LEFT: 'flex-start',
  RIGHT: 'flex-end',
  CENTER: 'center',
  JUSTIFIED: 'space-between'
};

const textVerticalAlignCssValues = {
  CENTER: 'center',
  BOTTOM: 'flex-end'
};

const textVerticalAlignCssValuesMap = {
  TOP: 'top',
  CENTER: 'middle',
  BOTTOM: 'bottom'
};

const textDecorationCssValues = {
  UNDERLINE: 'underline',
  STRIKETHROUGH: 'line-through'
};

export function getDynamicMarginOrPaddingValue(originValue: number[]): string | undefined {
  const [v1 = 0, v2 = 0, v3 = 0, v4 = 0] = originValue;

  if (v1 === 0 && v2 === 0 && v3 === 0 && v4 === 0) {
    return;
  }

  if (v1 === v2 && v2 === v3 && v3 === v4) {
    return buildSizeStringByUnit(v1);
  }

  if (v1 === v3 && v2 === v4) {
    return `${buildSizeStringByUnit(v1)} ${buildSizeStringByUnit(v2)}`;
  }

  if (v2 === v4) {
    return `${buildSizeStringByUnit(v1)} ${buildSizeStringByUnit(v2)} ${buildSizeStringByUnit(v3)}`;
  }

  return `${buildSizeStringByUnit(v1)} ${buildSizeStringByUnit(v2)} ${buildSizeStringByUnit(
    v3
  )} ${buildSizeStringByUnit(v4)}`;
}

const getPadding = (node: FrameNode) => {
  let { paddingTop = 0, paddingRight = 0, paddingBottom = 0, paddingLeft = 0, strokes, strokeWeight } = node;

  const borderWidth = strokes && strokes?.length > 0 && typeof strokeWeight === 'number' ? strokeWeight : 0;

  if (node.strokeAlign === 'INSIDE') {
    paddingTop = paddingTop - borderWidth;
    paddingRight = paddingRight - borderWidth;
    paddingBottom = paddingBottom - borderWidth;
    paddingLeft = paddingLeft - borderWidth;
  }

  return {
    paddingTop: paddingTop > 0 ? paddingTop : 0,
    paddingRight: paddingRight > 0 ? paddingRight : 0,
    paddingBottom: paddingBottom > 0 ? paddingBottom : 0,
    paddingLeft: paddingLeft > 0 ? paddingLeft : 0
  };
};

// Figma AutoLayout 元素的 Width 和 Height 实际渲染的尺寸，CSS 盒子模型默认是 Content-Box，需要处理 padding 和 border
const getContentHeight = (node: FrameNode, height: number) => {
  const { strokeWeight = 0, strokeAlign } = node;
  const { paddingTop = 0, paddingBottom = 0 } = getPadding(node as FrameNode);
  // @ts-ignore
  const borderWidth = node.strokes?.length > 0 && typeof strokeWeight === 'number' ? strokeWeight : 0;
  let contentHeight = height - paddingTop - paddingBottom;

  if (strokeAlign === 'INSIDE') {
    contentHeight = contentHeight - borderWidth - borderWidth;
  }

  return contentHeight > 0 ? Math.ceil(contentHeight) : height;
};

const getContentWidth = (node: FrameNode, width: number) => {
  const { strokeWeight, strokeAlign } = node;

  const { paddingLeft = 0, paddingRight = 0 } = getPadding(node as FrameNode);
  // @ts-ignore
  const borderWidth = node.strokes?.length > 0 && typeof strokeWeight === 'number' ? strokeWeight : 0;
  let contentWidth = width - paddingLeft - paddingRight;

  if (strokeAlign === 'INSIDE') {
    contentWidth = contentWidth - borderWidth - borderWidth;
  }

  return contentWidth > 0 ? Math.ceil(contentWidth) : width;
};

const getNodeRect = (node: FigmaNode, parentNode: FigmaNode | undefined) => {
  const { width, height } = (node as FrameNode).absoluteBoundingBox || {};
  const nodeRect = { width, height };

  if (
    !parentNode ||
    node.type === 'TEXT' ||
    // @ts-expect-error
    (parentNode.layoutMode !== 'HORIZONTAL' && parentNode.layoutMode !== 'VERTICAL')
  ) {
    return nodeRect;
  }

  const { width: parentWidth, height: parentHeight } = (parentNode as FrameNode).absoluteBoundingBox || {};

  const parentContentWidth = getContentWidth(parentNode as FrameNode, parentWidth || 0);
  const parentContentHeight = getContentHeight(parentNode as FrameNode, parentHeight || 0);

  if (typeof width === 'number' && typeof parentWidth === 'number' && width > parentContentWidth) {
    nodeRect.width = parentContentWidth;
  }

  if (typeof height === 'number' && typeof parentHeight === 'number' && height > parentContentHeight) {
    nodeRect.height = parentContentHeight;
  }

  return nodeRect;
};

const getNodeWidthAndHeight = (node: FrameNode | TextNode) => {
  const width = node.size?.x || node.absoluteBoundingBox?.width;
  const height = node.size?.y || node.absoluteBoundingBox?.height;
  const widthVal = typeof width === 'undefined' ? 'auto' : `${keepDecimal(width, 0)}px`;
  const heightVal = typeof height === 'undefined' ? 'auto' : `${keepDecimal(height, 0)}px`;

  return {
    widthVal,
    heightVal
  };
};

const getLineHeight = (style: TypeStyle, unitType: UnitType) => {
  const { lineHeightUnit, lineHeightPx, lineHeightPercentFontSize } = style;

  switch (lineHeightUnit) {
    case 'FONT_SIZE_%': {
      return lineHeightPercentFontSize;
    }
    case 'INTRINSIC_%': {
      return 'normal';
    }
    case 'PIXELS': {
      return buildSizeStringByUnit(lineHeightPx || 0, unitType);
    }
    default:
      return buildSizeStringByUnit(lineHeightPx || 0, unitType);
  }
};

const getLetterSpaceing = (letterSpacing: number, unitType: UnitType) => {
  return buildSizeStringByUnit(letterSpacing, unitType);
};

export const getWidthPropName = (node: FigmaNode): 'width' | 'minWidth' => {
  if (node.type === 'TEXT' && node?.styles?.textTruncation !== 'ENDING') {
    return 'minWidth';
  }

  return 'width';
};

const setParentPosition = (node: NodeDefinition, value: string) => {
  if (!node.styles) {
    node.styles = {};
  }

  if (!node.styles.position) {
    node.styles.position = value;
  }
};

export function radToDeg(rad: number) {
  return (rad * 180) / Math.PI;
}

export function getBackgroundColor(node: FigmaNode): string {
  // @ts-ignore
  const paint = node.fills?.find(
    (fill: Paint) =>
      ['SOLID', 'GRADIENT_LINEAR', 'GRADIENT_RADIAL', 'GRADIENT_ANGULAR'].includes(fill.type) &&
      ('visible' in fill ? fill.visible !== false : true)
  );

  try {
    switch (paint?.type) {
      case 'SOLID':
        return buildColor(paint);
      case 'GRADIENT_LINEAR':
      case 'GRADIENT_RADIAL':
      case 'GRADIENT_ANGULAR':
        return transformGradient(paint) || '';
      default:
        return '';
    }
  } catch (error) {
    return '';
  }
}

// @TODO: 需要验证 grid 布局 @cuileizhen
// 计算所有网格节点的开始和结束位置
function getCellStartAndEnd(gridItem: any, boxRect: Rectangle, type: 'COLUMNS' | 'ROWS') {
  const cell: GridCell = { start: [], end: [] };

  // 计算第一个单元的起点位置
  let begin = 0;

  switch (gridItem.alignment) {
    case 'MIN':
    case 'STRETCH':
      begin = gridItem.offset;
      break;
    case 'CENTER':
      begin = (boxRect.width - (gridItem.count - 1) * gridItem.gutterSize - gridItem.count * gridItem.sectionSize) / 2;
      break;
    case 'MAX':
      begin =
        boxRect.width -
        gridItem.offset -
        (gridItem.count - 1) * gridItem.gutterSize -
        gridItem.count * gridItem.sectionSize;
      break;
  }

  // 计算单元宽度
  let cellSize = 0;

  if (gridItem.alignment === 'STRETCH') {
    if (type === 'COLUMNS') {
      cellSize = (boxRect.width - 2 * gridItem.offset - (gridItem.count - 1) * gridItem.gutterSize) / gridItem.count;
    } else if (type === 'ROWS') {
      cellSize = (boxRect.height - 2 * gridItem.offset - (gridItem.count - 1) * gridItem.gutterSize) / gridItem.count;
    }
  } else {
    cellSize = gridItem.sectionSize;
  }

  // 计算每一个单元开始和结束位置
  for (let i = 0; i < gridItem.count; i++) {
    const start = begin + i * gridItem.gutterSize + i * cellSize;
    const end = start + cellSize;
    cell.start.push(Math.round(start));
    cell.end.push(Math.round(end));
  }

  return cell;
}

function getCellPosition(cell: GridCell, px: number, cx: number, cw: number): number[] {
  const tolerant = 3;
  const childStart = cx - px;
  const childEnd = childStart + cw;
  const position = [0, 0];

  for (let i = 0; i < cell.start.length; i++) {
    const dxStart = Math.abs(childStart - cell.start[i]);
    const dxEnd = Math.abs(childEnd - cell.end[i]);

    if (dxStart <= tolerant) {
      position[0] = i + 1;
    }

    if (dxEnd <= tolerant) {
      position[1] = i + 2;
    }
  }

  return position;
}

function isFlexContainer(node?: FrameNode): boolean {
  return node?.layoutMode === 'HORIZONTAL' || node?.layoutMode === 'VERTICAL';
}

function isGridContainer(node?: FrameNode): boolean {
  return !!node?.layoutGrids?.some((item) => item.visible !== false && ['COLUMNS', 'ROWS'].includes(item.pattern));
}

/** Start Properties Convertor */
const convertCommonStyle = (node: FigmaNode, styles: NodeStyle) => {
  if (!node.visible) {
    styles.display = 'none';
  }

  styles.boxSizing = 'border-box';
  //@ts-expect-error
  styles.overflow = node?.clipsContent ? 'hidden' : 'visible';

  if ('opacity' in node && node?.opacity && node?.opacity < 1) {
    styles.opacity = String(keepDecimal(node.opacity, 2));
  }

  if ((node as FrameNode).fills?.length > 0) {
    const gradients = (node as FrameNode).fills.filter((fill: Paint) => {
      return (
        fill.visible !== false &&
        ['GRADIENT_LINEAR', 'GRADIENT_RADIAL', 'GRADIENT_ANGULAR', 'GRADIENT_DIAMOND'].indexOf(fill.type) > -1
      );
    });
  }
};

const converMaxWidthHeight = (node: FigmaNode, styles: NodeStyle, unitType: UnitType) => {
  const properties = ['maxHeight', 'maxWidth', 'minHeight', 'minWidth'];

  properties.forEach((prop: string) => {
    if (node.hasOwnProperty(prop) && (node as any)[prop]) {
      (styles as any)[prop] = buildSizeStringByUnit(keepDecimal((node as any)[prop], 2), unitType);
    }
  });
};

// border、background、etc
const convertGeometry = (node: FrameNode | RectangleNode, styles: NodeStyle, unitType: UnitType) => {
  // 处理 border
  const solidStroke = node.strokes?.find((item) => item.visible !== false && item.type === 'SOLID');
  const strokeType = node.strokeAlign === 'INSIDE' ? 'border' : 'outline';

  if (solidStroke) {
    const strokeColor = buildColor(solidStroke);
    const strokeStyle = node.strokeDashes?.find((d) => d > 0) ? 'dashed' : 'solid';

    const { strokeWeight, individualStrokeWeights } = node;

    if (notEmptyNumber(strokeWeight)) {
      styles[`${strokeType}Width`] = buildSizeStringByUnit(strokeWeight);
      styles[`${strokeType}Style`] = strokeStyle;
      styles[`${strokeType}Color`] = strokeColor;
    } else if (individualStrokeWeights) {
      styles.borderWidth = getCSSShorthand(individualStrokeWeights, unitType);
      styles.borderColor = strokeColor;
      styles.borderStyle = strokeStyle;
    }
  }

  // 处理 border radius
  const { cornerRadius, rectangleCornerRadii } = node;
  const [topLeftRadius, topRightRadius, bottomRightRadius, bottomLeftRadius] = rectangleCornerRadii || [
    cornerRadius,
    cornerRadius,
    cornerRadius,
    cornerRadius
  ];

  if (notEmptyNumber(cornerRadius)) {
    styles.borderRadius = buildSizeStringByUnit(cornerRadius, unitType);
  } else if (rectangleCornerRadii) {
    styles.borderRadius = getCSSShorthand(
      {
        top: topLeftRadius || 0,
        right: topRightRadius || 0,
        bottom: bottomRightRadius || 0,
        left: bottomLeftRadius || 0
      },
      unitType
    );
  }

  const background = getBackgroundColor(node);

  if (background) {
    styles.background = background;
  }

  const boxShadow = getShadow(node as FrameNode);

  if (boxShadow) {
    styles['boxShadow'] = boxShadow;
  }
};

export const getBoundingBox = (nodeRect: Rectangle | null): AltRectangle => {
  const { x = 0, y = 0, width = 0, height = 0 } = nodeRect || {};

  return {
    x,
    y,
    width,
    height,
    top: y,
    left: x,
    bottom: y + height,
    right: x + width
  };
};

const convertBoundingBox = (node: FrameNode, parentNode: FrameNode | undefined) => {
  // 计算元素的宽和高：Figma 在渲染嵌套层级时，是不考虑 border 占用尺寸的，所以计算子元素的宽高时，需要考虑父元实际 content  的宽高，当子元素的宽高大于父元素的 content 宽高时，需要修改子元素的宽高
  const { width: nodeRectWidth, height: nodeRectHeight } = getNodeRect(node as FigmaNode, parentNode);

  // 更新 node 的 absoluteBoundingBox
  if (node.absoluteBoundingBox) {
    node.absoluteBoundingBox = {
      ...node.absoluteBoundingBox,
      width: nodeRectWidth || 0,
      height: nodeRectHeight || 0
    };
  }
};

const convertConstraintsLayout = (
  node: FrameNode | TextNode,
  styles: NodeStyle,
  parentNode: FrameNode,
  parentWorkNode?: NodeDefinition
) => {
  const width = node.size?.x ?? node.absoluteBoundingBox?.width ?? 0;
  const height = node.size?.y ?? node.absoluteBoundingBox?.height ?? 0;
  const { x, y } = getRawLeftTopCoordinate(node as FrameNode) || { x: 0, y: 0 };
  const { x: pWidth, y: pHeight } = parentNode?.size || { x: 0, y: 0 };
  const { x: px, y: py } = getRawLeftTopCoordinate(parentNode) || { x: 0, y: 0 };
  const { widthVal, heightVal } = getNodeWidthAndHeight(node as FrameNode);

  if (node?.constraints?.horizontal) {
    parentWorkNode && setParentPosition(parentWorkNode, 'relative');
    styles.position = 'absolute';
  }

  // 根据距离定位父元素距离的最小值设置绝对定位的方向
  const topDistance = y - py;
  const rightDistance = px + pWidth - x - width;
  const bottomDistance = py + pHeight - y - height;
  const leftDistance = x - px;

  styles[topDistance <= bottomDistance ? 'top' : 'bottom'] = `${Math.min(topDistance, bottomDistance)}px`;
  styles[leftDistance <= rightDistance ? 'left' : 'right'] = `${Math.min(leftDistance, rightDistance)}px`;

  if (node.type !== 'TEXT') {
    styles.width = widthVal;
    styles.height = heightVal;
  }
};

const convertFlexLayout = (node: FrameNode, styles: NodeStyle, unitType: UnitType) => {
  styles.display = 'flex';
  styles.flexDirection = node.layoutMode === 'HORIZONTAL' ? 'row' : 'column';
  styles.flexWrap = node.layoutWrap === 'WRAP' ? 'wrap' : 'nowrap';
  styles.justifyContent = node.primaryAxisAlignItems ? justifyContentCssValues[node.primaryAxisAlignItems] : 'normal';
  styles.alignItems = node.counterAxisAlignItems ? alignItemsCssValues[node.counterAxisAlignItems] : 'normal';
  styles.alignSelf = node.layoutAlign === 'STRETCH' ? 'stretch' : 'auto';

  const { paddingTop, paddingRight, paddingBottom, paddingLeft } = getPadding(node as FrameNode);

  styles.padding = getCSSShorthand(
    {
      top: paddingTop,
      right: paddingRight,
      bottom: paddingBottom,
      left: paddingLeft
    },
    unitType
  );

  // @ts-ignore
  if ((node as FrameNode).primaryAxisAlignItems !== 'SPACE_BETWEEN' && node.itemSpacing > 0) {
    styles.gap = buildSizeStringByUnit(node.itemSpacing || 0, unitType);
  }
};

const convertGridLayout = (node: FrameNode, styles: NodeStyle) => {
  styles.display = 'grid';

  const gridCols = node.layoutGrids?.find((item) => item.visible && item.pattern === 'COLUMNS');
  const gridRows = node.layoutGrids?.find((item) => item.visible && item.pattern === 'ROWS');

  if (gridCols) {
    const size = gridCols.alignment === 'STRETCH' ? '1fr' : `${gridCols.sectionSize}px`;
    styles.gridTemplateColumns = `repeat(${gridCols.count}, ${size})`;
    styles.gridColumnGap = `${gridCols.gutterSize}px`;
    styles.paddingLeft = `${gridCols.offset}px`;
    styles.paddingRight = `${gridCols.offset}px`;
    styles.justifyContent = gridCols.alignment ? gridJustifyContentCssValues[gridCols.alignment] : 'normal';
  }

  if (gridRows) {
    const size = gridRows.alignment === 'STRETCH' ? '1fr' : `${gridRows.sectionSize}px`;
    styles.gridTemplateRows = `repeat(${gridRows.count}, ${size})`;
    styles.gridRowGap = `${gridRows.gutterSize}px`;
    styles.paddingTop = `${gridRows.offset}px`;
    styles.paddingBottom = `${gridRows.offset}px`;
    styles.alignContent = gridJustifyContentCssValues[gridRows.alignment];
  }
};

export const convertText = (node: TextNode, styles: NodeStyle, unitType: UnitType) => {
  const { widthVal, heightVal } = getNodeWidthAndHeight(node);

  // https://www.figma.com/plugin-docs/api/properties/TextNode-textautoresize/
  switch (node.style?.textAutoResize) {
    case 'HEIGHT':
      styles.width = widthVal;
      delete styles.height;
      break;
    case 'WIDTH_AND_HEIGHT':
      styles.width = 'auto';
      delete styles.height;
      break;
    default:
      styles.width = widthVal;
      styles.height = heightVal;
      break;
  }

  if (node.style?.textAlignVertical !== 'TOP') {
    styles.alignItems = node.style?.textAlignVertical
      ? textVerticalAlignCssValues[node.style?.textAlignVertical]
      : 'normal';
    styles.justifyContent = node.style?.textAlignHorizontal
      ? textAlignCssFlexValues[node.style?.textAlignHorizontal]
      : 'normal';
  }

  styles.textAlign = node.style?.textAlignHorizontal ? textAlignCssValues[node.style?.textAlignHorizontal] : 'normal';
  styles.verticalAlign = node.style?.textAlignVertical
    ? textVerticalAlignCssValuesMap[node.style?.textAlignVertical]
    : 'normal';

  if (node.style?.fontSize) {
    styles.fontSize = `${keepDecimal(node.style?.fontSize)}px`;
  }

  if (node.style?.lineHeightPx) {
    const lineHeight = getLineHeight(node.style, unitType);

    if (lineHeight) {
      styles.lineHeight = lineHeight as string;
    }
  }

  if (node.style?.fontFamily) {
    styles.fontFamily = node.style?.fontFamily;
  }

  if (node.style?.fontWeight) {
    styles.fontWeight = node.style.fontWeight.toString();
  }

  if (node.style.letterSpacing && typeof node.style.letterSpacing !== 'symbol') {
    styles.letterSpacing = getLetterSpaceing(node.style.letterSpacing, 'px');
  }

  if (node.style.textDecoration === 'STRIKETHROUGH' || node.style.textDecoration === 'UNDERLINE') {
    styles.textDecoration = textDecorationCssValues[node.style.textDecoration];
  }

  if ((node.fills as Paint[])?.length > 0) {
    const colorPaint = (node.fills as Paint[]).find((item) => item.visible !== false && item.type === 'SOLID');

    if (colorPaint) {
      styles.color = colorPaint ? buildColor(colorPaint) : '';
    } else {
      const background = getBackgroundColor(node);

      if (background) {
        styles.background = background;
        styles.backgroundClip = 'text';
        styles.webkitBackgroundClip = 'text';
        styles.webkitTextFillColor = 'transparent';
      }
    }
  }

  const stroke = node.strokes?.find((item) => item.visible !== false && item.type === 'SOLID');

  if (stroke && notEmptyNumber(node.strokeWeight)) {
    styles.webkitTextStrokeWidth = buildSizeStringByUnit(node.strokeWeight);
    styles.webkitTextStrokeColor = buildColor(stroke);
  }

  const textShadow = getShadow(node);

  if (textShadow) {
    styles.textShadow = textShadow;
  }

  if (node.style.textTruncation === 'ENDING') {
    styles.whiteSpace = 'nowrap';
    styles.overflow = 'hidden';
    styles.textOverflow = 'ellipsis';
  }
};

const convertGridChildren = (node: FrameNode, styles: NodeStyle, parentNode: FrameNode) => {
  const { x, y, width, height } = node.absoluteBoundingBox || {};

  styles.position = 'static';

  const parentGridCols = parentNode.layoutGrids?.find((item) => item.visible && item.pattern === 'COLUMNS');
  const parentGridRows = parentNode.layoutGrids?.find((item) => item.visible && item.pattern === 'ROWS');
  const { x: parentX, y: parentY } = parentNode.absoluteBoundingBox ?? { x: 0, y: 0 };

  if (parentGridCols && parentNode.absoluteBoundingBox && x) {
    const parentGridColsCell = getCellStartAndEnd(parentGridCols, parentNode.absoluteBoundingBox, 'COLUMNS');
    const position = getCellPosition(parentGridColsCell, parentX, x, width || 0);

    if (position[0] != 0) {
      styles.gridColumnStart = String(position[0]);
    }

    if (position[1] != 0) {
      styles.gridColumnEnd = String(position[1]);
    }
  }

  if (parentGridRows && parentNode.absoluteBoundingBox && y && height) {
    const parentGridRowsCell = getCellStartAndEnd(parentGridRows, parentNode.absoluteBoundingBox, 'ROWS');
    const position = getCellPosition(parentGridRowsCell, parentY, y, height);

    if (position[0] != 0) {
      styles.gridRowStart = String(position[0]);
    }

    if (position[1] != 0) {
      styles.gridColumnEnd = String(position[1]);
    }
  }
};

const convertFlexChildren = (
  node: FrameNode | TextNode,
  styles: NodeStyle,
  parentNode: FrameNode | null,
  zIndex: number
) => {
  const { widthVal, heightVal } = getNodeWidthAndHeight(node);

  const parentFlexDirection = parentNode?.layoutMode === 'VERTICAL' ? 'column' : 'row';

  //@ts-expect-error
  styles.flexGrow = node.layoutGrow === 1 ? 1 : 0;
  //@ts-expect-error
  styles.flexShrink = node.layoutGrow === 1 ? 1 : 0;
  styles.flexBasis = node.layoutGrow === 1 ? 'auto' : parentFlexDirection === 'row' ? widthVal : heightVal;

  styles[getWidthPropName(node)] = widthVal;

  if (node.type !== 'TEXT') {
    styles.height = heightVal;
  }

  if (node.layoutAlign === 'STRETCH') {
    styles.alignSelf = 'stretch';
  } else {
    styles[parentFlexDirection === 'row' ? 'height' : 'width'] = parentFlexDirection === 'row' ? heightVal : widthVal;
  }

  // 处理元素之间的 margin
  if (parentNode && parentNode.itemSpacing && parentNode.itemSpacing < 0 && zIndex >= 1) {
    const marginProperty = parentNode.layoutMode === 'HORIZONTAL' ? 'marginLeft' : 'marginTop';
    styles[marginProperty] = `${parentNode.itemSpacing}px`;
  }

  // 如果是 Flex 子元素覆盖顺序是逆序的，调整 z-index 的值
  // parentFrame.itemReverseZIndex = false
  // Parent frame (last child on top)
  // +---------------------+
  // |+-------+-----------+|
  // ||       |           ||
  // ||Child 1|  Child 2  ||
  // ||       |           ||
  // |+-------+-----------+|
  // +---------------------+
  // parentFrame.itemReverseZIndex = true
  // Parent frame (first child on top)
  // +---------------------+
  // |+-----------+-------+|
  // ||           |       ||
  // ||  Child 1  |Child 2||
  // ||           |       ||
  // |+-----------+-------+|
  // +---------------------+
  if (parentNode?.itemReverseZIndex === true) {
    // @ts-ignore
    styles.zIndex = parentNode.children.length - zIndex;
  }
};

const convertWidthHeight = (
  node: FrameNode | TextNode,
  styles: NodeStyle,
  parentNode: FrameNode | null,
  isUseAbsolute = false
) => {
  if (node.type === 'TEXT') {
    return;
  }

  const { widthVal, heightVal } = getNodeWidthAndHeight(node);

  if (!isUseAbsolute && isFlexContainer(node)) {
    switch ((node as FrameNode).layoutSizingHorizontal) {
      case 'FIXED':
        styles.width = widthVal;
        break;
      case 'HUG':
        // figma 有 bug，设置 fit-content 实际是按照固定宽度展示的，预期是 styles.width = 'fit-content'; 处理成 auto
        break;
      case 'FILL':
        // styles.width = 'auto';
        break;
      default:
        styles.width = widthVal;
        break;
    }

    switch ((node as FrameNode).layoutSizingVertical) {
      case 'FIXED':
        styles.height = heightVal;
        break;
      case 'HUG':
        // figma 有 bug，设置 fit-content 实际是按照固定宽度展示的，预期是 styles.width = 'fit-content'; 处理成 auto
        break;
      case 'FILL':
        // styles.height = 'auto';
        break;
      default:
        styles.height = heightVal;
        break;
    }
  } else if (!parentNode?.absoluteBoundingBox) {
    styles.height = heightVal;
    styles.width = widthVal;
  }

  // 最顶层图层，设置有固定的宽度和高度
  if (!parentNode) {
    styles.height = heightVal;
    styles.width = widthVal;
  }
};

export function resolveCssForNode(params: {
  node: FigmaNode;
  parentNode: FrameNode | null;
  parentWorkNode?: NodeDefinition;
  unitType: UnitType;
  zIndex: number;
  isUseAbsolute?: boolean;
}): NodeStyle {
  const { node, parentNode, parentWorkNode, unitType, zIndex, isUseAbsolute = false } = params;
  const isParentDisplayFlex = parentNode ? isFlexContainer(parentNode) : false;
  const isParentDisplayGrid = parentNode ? isGridContainer(parentNode) : false;
  const cssStyles: NodeStyle = {};

  convertCommonStyle(node, cssStyles);
  converMaxWidthHeight(node, cssStyles, unitType);

  if (isParentDisplayGrid && (node as FrameNode).layoutPositioning !== 'ABSOLUTE' && parentNode) {
    convertGridChildren(node as FrameNode, cssStyles, parentNode);
  }

  if (isParentDisplayFlex) {
    convertFlexChildren(node as FrameNode, cssStyles, parentNode, zIndex);
  }

  convertWidthHeight(node as FrameNode, cssStyles, parentNode, isUseAbsolute);

  // constraints 布局对宽度和高度的影响高于 AutoLayout，需要放在 convertWidthHeight 执行
  if (
    parentNode?.absoluteBoundingBox &&
    (!isParentDisplayFlex || (node as FrameNode).layoutPositioning === 'ABSOLUTE')
  ) {
    convertConstraintsLayout(node as FrameNode, cssStyles, parentNode, parentWorkNode);
  }

  if (node.type === 'FRAME' || node.type === 'INSTANCE' || node.type === 'COMPONENT') {
    convertGeometry(node as FrameNode | RectangleNode, cssStyles, unitType);

    // 处理 GridLayout
    if (!isUseAbsolute && isGridContainer(node as FrameNode)) {
      convertGridLayout(node as FrameNode, cssStyles);
    }

    // 处理 AutoLayout
    if (!isUseAbsolute && isFlexContainer(node as FrameNode)) {
      convertFlexLayout(node as FrameNode, cssStyles, unitType);
    }
  }

  if (node.type === 'RECTANGLE') {
    convertGeometry(node as RectangleNode, cssStyles, unitType);
  }

  if (node.type === 'TEXT') {
    convertText(node, cssStyles, unitType);
  }

  if (node.type === 'LINE') {
    // @ts-ignore
    convertGeometry(node as LineNode, cssStyles, unitType);
  }

  convertRotation(node, cssStyles);

  return cssStyles;
}

// 处理旋转
const convertRotation = (node: FigmaNode, styles: NodeStyle) => {
  if (
    [
      'BOOLEAN_OPERATION',
      'COMPONENT',
      'COMPONENT_SET',
      'ELLIPSE',
      'FRAME',
      'GROUP',
      'HIGHLIGHT',
      'INSTANCE',
      'LINE',
      'POLYGON',
      'RECTANGLE',
      'SLICE',
      'STAMP',
      'STAR',
      'TEXT',
      'VECTOR',
      'WASHI_TAPE'
    ].includes(node.type) &&
    notEmptyNumber(node.rotation)
  ) {
    styles.transform = `rotate(${keepDecimal(radToDeg(node.rotation))}deg)`;
  }
};
