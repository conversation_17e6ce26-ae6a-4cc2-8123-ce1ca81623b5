import { Node as FigmaMixNode, FrameNode, TextNode } from '@figma/rest-api-spec';
import type { OneNodeWorkResult, NodeDefinition, FigmaNode } from '../types';
import type { PerformUnitOfWorkResult } from '../traverse';
import type { TraverseContext } from '../index';

import { omitBy, isEmpty, pick, omit } from 'lodash-es';
import { getBoundingBox, resolveCssForNode, convertText } from './resolve-style';
import { isNotAffectAppearance, isImage, isText, removeRedundancyStyle, restTextSegmentsFills } from '../utils';
import { removeInnerLayoutStyle, handleCommonStyles, removeAbsoluteProperties } from '../utils';
import { LAYOUT_PROPS } from '../const';
import { currentLoad } from 'systeminformation';

export async function convertNode(
  rootNode: FigmaMixNode, // 根节点
  parentNode: FigmaNode | undefined, // 父节点
  curNode: FigmaNode, // 当前节点
  traverseContext: TraverseContext, // 遍历上下文
  parentWorkResult: OneNodeWorkResult, // 父节点处理结果
  index: number, // 当前节点索引
): Promise<PerformUnitOfWorkResult<OneNodeWorkResult | undefined>> {
  // 兼容处理一些特殊属性
  curNode.visible = curNode.visible ?? true;
  curNode.opacity = curNode.opacity ?? 1;

  // 过滤无用图层
  if (isNotAffectAppearance(curNode as FrameNode)) {
    return {
      shouldTraverseChildren: false,
      result: {
        node: undefined,
      },
    };
  }

  let shouldTraverseChildren = true;

  const rawStyles = resolveCssForNode({
    node: curNode,
    parentNode: parentNode as FrameNode | null,
    parentWorkNode: parentWorkResult.node,
    zIndex: index,
    unitType: 'px',
  });

  const altNode: NodeDefinition = {
    id: curNode.id,
    parentId: parentNode?.id ?? '',
    nodeType: 'view',
    name: curNode.name,
    boundingRect: getBoundingBox(curNode.absoluteBoundingBox),
    renderBoundingRect: getBoundingBox(curNode.absoluteRenderBounds),
    styles: removeRedundancyStyle(rawStyles, curNode.type),
    zIndex: index + 1,
    characters: (curNode as TextNode).characters,
    children: [],
  };

  traverseContext.nodeRawMap[curNode.id] = curNode;

  if (curNode.type === 'INSTANCE') {
    altNode.componentId = curNode.componentId;
    altNode.componentProperties = Object.entries(curNode.componentProperties ?? {}).map(
      ([name, { value, type }]) => ({
        name,
        value: value.toString(),
        type,
      }),
    );
  }

  // 互斥的节点类型识别：节点类型来源于标记识别、特征值识别；
  if (isImage(curNode)) {
    altNode.nodeType = 'image';
    altNode.styles = removeInnerLayoutStyle(altNode.styles);
    shouldTraverseChildren = false;

    traverseContext.imageNodes.push(curNode.id);

  } else if (isText(curNode)) {
    altNode.nodeType = 'text';
    shouldTraverseChildren = false;

    // 处理 mixed
    const textSegmentsFills = restTextSegmentsFills(curNode as TextNode);
    if (textSegmentsFills?.length > 0) {
      let textFlag = '';

      // 处理 mixed 样式 text 图层
      textSegmentsFills.forEach((segment, index) => {
        if (typeof segment.styleMapId !== "undefined") {
          textFlag += segment.characters;

          if (!segment.characters) {
            return;
          }

          const segmentStyle = {};
          const childNode = {
            ...curNode,
            ...omitBy(segment, (val) => typeof val === 'object' && isEmpty(val)),
          }

          convertText((childNode as TextNode), segmentStyle, 'px');

          const newNode: NodeDefinition = {
            id: `${curNode.id}:${index}`,
            nodeType: 'text',
            name: `${curNode.name}_${index}`,
            boundingRect: getBoundingBox((curNode as FrameNode).absoluteBoundingBox),
            renderBoundingRect: getBoundingBox((curNode as FrameNode).absoluteBoundingBox),
            styles: segmentStyle,
            // 去除半角空格，全角空格，零宽字符
            characters: segment.characters.replace(/[\u0020|\u3000|\u200B-\u200D|\uFEFF]/g, ''),
            children: [],
          };

          altNode.children?.push(newNode);
        }
      });

      // figma bug：如果图层有换行，且最后的几行不是 mixed，textSegmentsFills 里不会包含对应的信息，需要处理
      if (textFlag.length < (curNode as TextNode).characters.length) {
        const restIndex = textSegmentsFills.length + 1;
        const restChar = (curNode as TextNode).characters.slice(textFlag.length);

        altNode.children.push({
          id: `${curNode.id}:${restIndex}`,
          nodeType: 'text',
          name: `${curNode.name}_${restIndex}`,
          boundingRect: getBoundingBox((curNode as FrameNode).absoluteBoundingBox),
          renderBoundingRect: getBoundingBox((curNode as FrameNode).absoluteBoundingBox),
          styles: altNode.styles,
          characters: restChar,
          children: [],
        });
      }

      if (altNode.children?.length > 0) {
        altNode.nodeType = 'text';
        altNode.name = altNode.children.map((child) => child.name).join('_');
        altNode.characters = '';

        // 提取公共样式，布局样式，不需要处理，
        const {
          commonStyles,
          uniqStyles
        } = handleCommonStyles(altNode.children.map((child) => omit(child.styles, LAYOUT_PROPS)));

        if (Object.keys(commonStyles)?.length > 0) {
          const containerStyle = pick(altNode.styles, LAYOUT_PROPS);

          altNode.styles = {
            ...containerStyle,
            ...commonStyles,
          };
          altNode.children.forEach((child, index) => {
            child.styles = uniqStyles[index];
          });
        }
      }
    }

    // 删除 Text 子元素的绝对定位样式
    altNode.children.forEach((child) => {
      removeAbsoluteProperties(child);
    });
  }

  if (altNode) {
    parentWorkResult?.node?.children?.push(altNode);
  }

  return {
    shouldTraverseChildren,
    result: {
      node: altNode,
    },
  };
}
