import { Component, ComponentSet } from '@figma/rest-api-spec';
import type { NodeDefinition, ComponentDefinition, ComponentSetDefinition } from '../types';

export const keepDecimal = (value: number, pointNum = 2) => {
  if (!Number(value)) {
    return 0;
  }

  return Number(value.toFixed(pointNum));
};

export const notEmptyNumber = (value: unknown): value is number => {
  // 兼容 figma 字段精度问题
  return typeof value === 'number' && Number(keepDecimal(value)) !== 0;
};

export const buildSizeStringByUnit = (pixelValue: number, type: 'px' | 'rem' = 'px', unit = 16): string => {
  if (type === 'px') {
    return `${keepDecimal(pixelValue)}px`;
  }

  if (type === 'rem') {
    return `${keepDecimal(pixelValue / unit)}rem`;
  }

  return `${keepDecimal(pixelValue / 10)}rem`;
};

/**
 * 取数组里的中间值元素
 */
export const getMedianOfArr = (arr: number[]): number => {
  if (arr.length <= 2) {
    return Math.min(...arr);
  }

  const middleIndex = (arr.length + 1) / 2;
  const sorted = [...arr].sort((a, b) => a - b);
  const isEven = sorted.length % 2 === 0;

  return isEven ? sorted[middleIndex - 1.5] : sorted[middleIndex - 1];
};

export const compressAltNode = (node: any): void => {
  function compressNode(nodeEle: any) {
    // 删除对模型无用的字段
    delete nodeEle.layoutType;
    delete nodeEle.parentId;
    delete nodeEle.id;
    delete nodeEle.name;

    if (nodeEle.children && nodeEle.children.length > 0) {
      nodeEle.children.forEach((child: any) => compressNode(child));
    }
  }

  compressNode(node);
};

export const transformComponet = (components: Record<string, Component>): Record<string, ComponentDefinition> => {
  return Object.fromEntries(
    Object.entries(components).map(([componentId, componentInfo]) => [
      componentId,
      {
        id: componentId,
        name: componentInfo.name,
        description: componentInfo.description,
        componentSetId: componentInfo.componentSetId
      }
    ])
  );
};

export const transformComponetSet = (
  componentSets: Record<string, ComponentSet>
): Record<string, ComponentSetDefinition> => {
  return Object.fromEntries(
    Object.entries(componentSets).map(([componentSetId, componentSetInfo]) => [
      componentSetId,
      {
        id: componentSetId,
        name: componentSetInfo.name,
        description: componentSetInfo.description
      }
    ])
  );
};
