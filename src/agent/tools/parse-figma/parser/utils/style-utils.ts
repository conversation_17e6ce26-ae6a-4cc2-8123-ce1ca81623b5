import { FrameNode, TextNode, Paint, RGB, DropShadowEffect, InnerShadowEffect } from '@figma/rest-api-spec';
import { NodeStyle, TextSegmentsFillItem } from '../types';

import { omit } from 'lodash-es';
import { keepDecimal } from './common';
import { LAYOUT_PROPS } from '../const';
import { gradientHandlePositions2GradientTransform } from './gradient-transform/';

/**
 * 线性背景颜色计算方法
 * https://github.com/Nushaine/calculateLinearGradient-FigmaAPI
 */
// GENERAL FUNCTIONS
export function round(num: number, decimalPlaces: number) {
  return parseFloat(num.toFixed(decimalPlaces));
}

/**
 * FUNC_DES: RGB 对象转 RGB 字符串
 */
export function RGBAObject2String(object: any, opacity: any) {
  return (
    'rgba('
    + String(round(object.r * 255, 0))
    + ','
    + String(round(object.g * 255, 0))
    + ','
    + String(round(object.b * 255, 0))
    + ','
    + String(round(opacity, 2))
    + ')');
}

// LINEAR GRADIENT CALCULATION FUNCTIONS
export function calculateIntersection(p1: any, p2: any, p3: any, p4: any) {
  // usage: https://dirask.com/posts/JavaScript-how-to-calculate-intersection-point-of-two-lines-for-given-4-points-VjvnAj

  // down part of intersection point formula
  const d1 = (p1[0] - p2[0]) * (p3[1] - p4[1]); // (x1 - x2) * (y3 - y4)
  const d2 = (p1[1] - p2[1]) * (p3[0] - p4[0]); // (y1 - y2) * (x3 - x4)
  const d = d1 - d2;

  if (d === 0) {
    throw new Error('Number of intersection points is zero or infinity.');
  }

  // upper part of intersection point formula
  const u1 = p1[0] * p2[1] - p1[1] * p2[0]; // (x1 * y2 - y1 * x2)
  const u4 = p3[0] * p4[1] - p3[1] * p4[0]; // (x3 * y4 - y3 * x4)

  const u2x = p3[0] - p4[0]; // (x3 - x4)
  const u3x = p1[0] - p2[0]; // (x1 - x2)
  const u2y = p3[1] - p4[1]; // (y3 - y4)
  const u3y = p1[1] - p2[1]; // (y1 - y2)

  // intersection point formula

  const px = (u1 * u2x - u3x * u4) / d;
  const py = (u1 * u2y - u3y * u4) / d;

  return {
    x: round(px, 2),
    y: round(py, 2),
  };
}

export function rotate(cx: number, cy: number, x: number, y: number, angle: number) {
  const radians = (Math.PI / 180) * angle,
    cos = Math.cos(radians),
    sin = Math.sin(radians),
    nx = cos * (x - cx) + sin * (y - cy) + cx,
    ny = cos * (y - cy) - sin * (x - cx) + cy;

  return [nx, ny];
}

export function rotateElipse(
  cx: number,
  cy: number,
  xRadius: number,
  yRadius: number,
  angle: number,
  rotationFactor: number,
) {
  // 'https://www.desmos.com/calculator/aqlhivzbvs'; // -> rotated elipse equations
  // 'https://www.mathopenref.com/coordparamellipse.html'; // -> good explanation about elipse parametric equations
  // 'https://math.stackexchange.com/questions/941490/whats-the-parametric-equation-for-the-general-form-of-an-ellipse-rotated-by-any?noredirect=1&lq=1&newreg=fd8890e3dad245b0b6a0f182ba22f7f3'; // -> good explanation of rotated parametric elipse equations
  // rotates points[x, y] some degrees about an origin [cx, cy]
  xRadius = xRadius * 1.5;
  yRadius = yRadius * 1.5;

  const normalizedRotationFactor = rotationFactor / 57.29577951;
  const cosAngle = Math.cos((Math.PI / 180) * (angle + 180));
  const sinAngle = Math.sin((Math.PI / 180) * (angle + 180));
  const x = -xRadius * Math.cos(normalizedRotationFactor) * cosAngle - yRadius * Math.sin(normalizedRotationFactor) * sinAngle + cx;
  const y = -yRadius * Math.cos(normalizedRotationFactor) * sinAngle + xRadius * Math.sin(normalizedRotationFactor) * cosAngle + cy;

  return [x, y];
}

export function getCorners(component: any, elemRotation: number) {
  // gets all 4 corners of a vector even if vector rotated
  const topLeft = [component.relativeTransform[0][2], component.relativeTransform[1][2]];
  const topRight = rotate(topLeft[0], topLeft[1], topLeft[0] + component.size.x, topLeft[1], -elemRotation);
  const bottomRight = rotate(topRight[0], topRight[1], topRight[0], topRight[1] + component.size.y, -elemRotation);
  const bottomLeft = rotate(
    bottomRight[0],
    bottomRight[1],
    bottomRight[0] - component.size.x,
    bottomRight[1],
    -elemRotation,
  );

  return {
    topLeft,
    topRight,
    bottomLeft,
    bottomRight,
  };
}

export function calculateAngle(x1: number, y1: number, x2: number, y2: number) {
  let angle = Math.round((180 / Math.PI) * Math.atan((y2 - y1) / (x2 - x1)) * 100) / 100;

  const rounded = {
    x1: Math.round(x1 * 100) / 100,
    x2: Math.round(x2 * 100) / 100,
    y1: Math.round(y1 * 100) / 100,
    y2: Math.round(y2 * 100) / 100
  };

  if (rounded.x1 < rounded.x2) {
    angle = angle + 180; // in quad 2 & 3
  } else if (rounded.x1 > rounded.x2) {
    if (rounded.y1 < rounded.y2) {
      angle = 360 - Math.abs(angle); // in quad 4
    }
  } else if (rounded.x1 == rounded.x2) {
    // horizontal line
    if (rounded.y1 < rounded.y2) {
      angle = 360 - Math.abs(angle); // on negative y-axis
    } else {
      angle = Math.abs(angle); // on positive y-axis
    }
  }
  return Math.round(angle * 100) / 100;
}

export function getGradientPoints(topLeftCorner: any, pointRelativeCoords: any, shapeCenter: any, elemRotate: number) {
  return rotate(
    topLeftCorner[0],
    topLeftCorner[1],
    topLeftCorner[0] + pointRelativeCoords[0],
    topLeftCorner[1] + pointRelativeCoords[1],
    elemRotate,
  );
}

export function createLinearGradient(fill: any, component: any) {
  const gradientAngle = calculateAngle(
    fill.gradientHandlePositions[2].x,
    fill.gradientHandlePositions[2].y,
    fill.gradientHandlePositions[0].x,
    fill.gradientHandlePositions[0].y
  );

  // this next section finds the linear gradient line segment -> https://stackoverflow.com/questions/51881307 creating-a-css-linear-gradient-based-on-two-points-relative-to-a-rectangle

  // calculating gradient line size (scalar) and change in x, y direction (coords)
  const lineChangeCoords = [
    (fill.gradientHandlePositions[1].x - fill.gradientHandlePositions[0].x) * component.size.x,
    (1 - fill.gradientHandlePositions[1].y - (1 - fill.gradientHandlePositions[0].y)) * component.size.y
  ];
  const currentLineSize = Math.sqrt(lineChangeCoords[0] ** 2 + lineChangeCoords[1] ** 2);

  // creating arbitrary gradient line
  const desiredLength = ((component.size.x + component.size.y) / 2) * 4;
  const scaleFactor = (desiredLength - currentLineSize) / 2 / currentLineSize;
  const scaleCoords = [lineChangeCoords[0] * scaleFactor, lineChangeCoords[1] * scaleFactor];

  const elemRotate = -Math.acos(component.relativeTransform[0][0]) * (180 / Math.PI);
  const corners = getCorners(component, elemRotate);

  const shapeCenter = calculateIntersection(corners.topLeft, corners.bottomRight, corners.topRight, corners.bottomLeft);

  const scaledArbGradientLine = [
    getGradientPoints(
      [corners.topLeft[0], corners.topLeft[1]],
      [
        fill.gradientHandlePositions[0].x * component.size.x - scaleCoords[0],
        fill.gradientHandlePositions[0].y * component.size.y + scaleCoords[1]
      ],
      [shapeCenter.x, shapeCenter.y],
      elemRotate,
    ),
    getGradientPoints(
      [corners.topLeft[0], corners.topLeft[1]],
      [
        fill.gradientHandlePositions[1].x * component.size.x + scaleCoords[0],
        fill.gradientHandlePositions[1].y * component.size.y - scaleCoords[1]
      ],
      [shapeCenter.x, shapeCenter.y],
      elemRotate,
    ),
  ];
  // getting relevant corners
  const centers = {
    top:
      (gradientAngle > 90 && gradientAngle <= 180) || (gradientAngle > 270 && gradientAngle <= 360)
        ? corners.topLeft
        : corners.topRight,
    bottom:
      (gradientAngle >= 0 && gradientAngle <= 90) || (gradientAngle > 180 && gradientAngle <= 270)
        ? corners.bottomLeft
        : corners.bottomRight,
  };

  // creating perpendicular lines
  const topLine = [
    rotate(centers.top[0], centers.top[1], centers.top[0] - desiredLength / 2, centers.top[1], elemRotate),
    rotate(centers.top[0], centers.top[1], centers.top[0] + desiredLength / 2, centers.top[1], elemRotate)
  ];
  const rotatedtopLine = [
    rotateElipse(
      centers.top[0],
      centers.top[1],
      centers.top[0] - topLine[0][0],
      (centers.top[0] - topLine[0][0]) * (component.size.y / component.size.x),
      gradientAngle,
      elemRotate,
    ),
    rotateElipse(
      centers.top[0],
      centers.top[1],
      centers.top[0] - topLine[1][0],
      (centers.top[0] - topLine[1][0]) * (component.size.y / component.size.x),
      gradientAngle,
      elemRotate,
    )
  ];
  const bottomLine = [
    rotate(centers.bottom[0], centers.bottom[1], centers.bottom[0] - desiredLength / 2, centers.bottom[1], elemRotate),
    rotate(centers.bottom[0], centers.bottom[1], centers.bottom[0] + desiredLength / 2, centers.bottom[1], elemRotate)
  ];
  const rotatedbottomLine = [
    rotateElipse(
      centers.bottom[0],
      centers.bottom[1],
      centers.bottom[0] - bottomLine[0][0],
      (centers.bottom[0] - bottomLine[0][0]) * (component.size.y / component.size.x),
      gradientAngle,
      elemRotate,
    ),
    rotateElipse(
      centers.bottom[0],
      centers.bottom[1],
      centers.bottom[0] - bottomLine[1][0],
      (centers.bottom[0] - bottomLine[1][0]) * (component.size.y / component.size.x),
      gradientAngle,
      elemRotate,
    ),
  ];
  const perpLines = {
    top: rotatedtopLine,
    bottom: rotatedbottomLine,
  };

  // calculating relevant portion of gradient line (the actual gradient line -> taking POI of perpendicular lines w/ arbitrary gradient line)

  const topLineIntersection = calculateIntersection(
    perpLines.top[0],
    perpLines.top[1],
    scaledArbGradientLine[0],
    scaledArbGradientLine[1],
  );
  const bottomLineIntersection = calculateIntersection(
    perpLines.bottom[0],
    perpLines.bottom[1],
    scaledArbGradientLine[0],
    scaledArbGradientLine[1],
  );

  const gradientLine = {
    topCoords: topLineIntersection,
    bottomCoords: bottomLineIntersection,
  };
  const gradientLineDistance = Math.sqrt(
    (gradientLine.bottomCoords.y - gradientLine.topCoords.y) ** 2
    + (gradientLine.bottomCoords.x - gradientLine.topCoords.x) ** 2,
  );

  const rounded = {
    x1: Math.round(fill.gradientHandlePositions[0].x * 100) / 100,
    x2: Math.round(fill.gradientHandlePositions[1].x * 100) / 100,
    y1: Math.round(fill.gradientHandlePositions[0].y * 100) / 100,
    y2: Math.round(fill.gradientHandlePositions[1].y * 100) / 100
  };

  const absoluteStartingPoint = getGradientPoints(
    corners.topLeft,
    [fill.gradientHandlePositions[0].x * component.size.x, fill.gradientHandlePositions[0].y * component.size.y],
    [corners.topLeft[0] + component.size.x / 2, corners.topLeft[1] + component.size.y / 2],
    elemRotate,
  );

  const colorStr = fill.gradientStops
    .map((color: any) => {
      // formatting rgb values into string
      const colorObj = {
        r: color.color.r,
        g: color.color.g,
        b: color.color.b,
      };
      const fillOpacity = fill.opacity === undefined ? 1 : fill.opacity;
      const formattedColor = RGBAObject2String(colorObj, color.color.a * fillOpacity);

      let gradientStartPoint;

      if (rounded.y1 < rounded.y2) {
        gradientStartPoint = gradientLine.topCoords.y < gradientLine.bottomCoords.y ? gradientLine.topCoords : gradientLine.bottomCoords;
      } else {
        gradientStartPoint = gradientLine.topCoords.y > gradientLine.bottomCoords.y ? gradientLine.topCoords : gradientLine.bottomCoords;
        // HORIZONTAL OR VERTICAL LINES?????
      }

      const colorX = color.position * lineChangeCoords[0] + absoluteStartingPoint[0];
      const colorY = absoluteStartingPoint[1] - color.position * lineChangeCoords[1];
      const colorDistance = Math.sqrt((colorY - gradientStartPoint.y) ** 2 + (colorX - gradientStartPoint.x) ** 2);
      const actualPercentage = colorDistance / gradientLineDistance;

      // colorStr += `${formattedColor} ${Math.round(actualPercentage * 10000) / 100}%, `
      return `${formattedColor} ${Math.round(actualPercentage * 10000) / 100}%`;
    })
    .join(', ');

  return ` linear-gradient(${gradientAngle}deg, ${colorStr}) `;
}

export function mergeNodeStyles(baseNodeStyle: NodeStyle, childNodeStyle: NodeStyle): NodeStyle {
  const mergedStyle = { ...baseNodeStyle };

  // 处理 childrenNode 样式里的默认值
  if (childNodeStyle.display === 'flex') {
    !childNodeStyle.flexDirection && (childNodeStyle.flexDirection = 'row');
    // @ts-ignore
    !childNodeStyle.gap && (childNodeStyle.gap = 0);
  }

  for (const cssKey in childNodeStyle) {
    // 不合并定位相关的属性
    if (!LAYOUT_PROPS.includes(cssKey)) {
      mergedStyle[cssKey] = childNodeStyle[cssKey];
    }
  }

  // 处理子元素 position 为 relative，父元素没有设置 position 的场景
  if (!mergedStyle.position && childNodeStyle.position === 'relative') {
    mergedStyle.position = 'relative';
  }

  return mergedStyle;
}

export function removeApperanceStyle(styles: NodeStyle) {
  const newStyles: NodeStyle = {};

  for (const key in styles) {
    if (LAYOUT_PROPS.includes(key)) {
      newStyles[key] = styles[key];
    }
  }

  return newStyles;
}

export function removeInnerLayoutStyle(styles: NodeStyle) {
  return omit(styles, ['padding', 'paddingLeft', 'paddingRight', 'paddingTop', 'paddingBottom', 'border', 'transform', 'boxShadow', 'opacity']);
}

/**
 * 提取一组 CSS 里重复的属性
 * @param styleList 
 * @returns 
 */
export function handleCommonStyles(styleList: NodeStyle[]): {
  commonStyles: NodeStyle;
  uniqStyles: NodeStyle[];
} {
  if (styleList.length === 0) {
    return {
      commonStyles: {},
      uniqStyles: [],
    };
  }

  const commonStyles: NodeStyle = {};
  const first = styleList[0];
  const restStyles = styleList.slice(1);

  for (const prop in first) {
    const propVal = first[prop]
    const isSame = restStyles.every(style => style[prop] === propVal);

    if (isSame) {
      commonStyles[prop] = propVal;
    }
  }

  const commonKeys = Object.keys(commonStyles);
  const uniqStyles: NodeStyle[] = styleList.map(style => {
      const newStyle: NodeStyle = { ...style };

      for (const key of commonKeys) {
          delete newStyle[key as keyof NodeStyle];
      }

      return newStyle;
  });

  return {
    commonStyles,
    uniqStyles,
  }
}

export function radToDeg(rad: number) {
  return (rad * 180) / Math.PI;
}

/**
 * 将 RGBA 颜色值转换为 HEX 字符串
 * @param color RGB 对象，r/g/b 分量 0~1
 * @param opacity 透明度分量 0~1，默认 1
 * @returns HEX 字符串，如 #RRGGBB 或 #RRGGBBAA
 */
export function rgbaToHex(color: RGB, opacity: number = 1): string {
  // 边界保护，确保分量在 0~1 之间
  const clamp = (v: number) => Math.max(0, Math.min(1, v));
  const toHex = (v: number) => Math.round(clamp(v) * 255).toString(16).padStart(2, '0').toUpperCase();
  const hex = `#${toHex(color.r)}${toHex(color.g)}${toHex(color.b)}`;

  return clamp(opacity) < 1
    ? `${hex}${toHex(opacity)}`
    : hex;
}

export function buildColor(paint: Paint) {
  return paint.type === 'SOLID'
    ? rgbaToHex(paint.color, paint.opacity)
    : '';
}

export const getShadow = (node: FrameNode | TextNode): string => {
  const effectTypes = node.type === 'TEXT'
    ? ['DROP_SHADOW']
    : ['DROP_SHADOW', 'INNER_SHADOW'];
  const visibleEffects = node.effects?.filter(effect => {
    return effect.visible && effectTypes.includes(effect.type ?? '');
  }) ?? [];

  return visibleEffects.map(effect => {
    const {
      type,
      color,
      radius,
      spread,
      offset = { x: 0, y: 0 },
    } = effect as (DropShadowEffect | InnerShadowEffect);

    const offsetX = offset.x === 0 ? 0 :`${keepDecimal(offset.x)}px`;
    const offsetY = offset.y === 0 ? 0 : `${keepDecimal(offset.y)}px`;
    const radiusVal = radius ? `${keepDecimal(radius)}px` : '';
    const spreadVal = spread ? `${keepDecimal(spread)}px` : '';
    const shadowColor = rgbaToHex(color, color.a);

    return [
      `${type === 'INNER_SHADOW' ? 'inset' : ''}`,
      `${offsetX}`,
      `${offsetY}`,
      `${radiusVal}`,
      `${spreadVal}`,
      `${shadowColor}`
    ].filter(item => item).join(' ');
  }).join(',');
}

export function getCSSShorthand(
  values: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  },
  suffix: string = 'px',
) {
  const { top, right, bottom, left } = values;

  if (top === right && right === bottom && bottom === left) {
    return `${top}${suffix}`;
  }

  if (right === left) {
    if (top === bottom) {
      return `${top}${suffix} ${right}${suffix}`;
    }
    return `${top}${suffix} ${right}${suffix} ${bottom}${suffix}`;
  }
  return `${top}${suffix} ${right}${suffix} ${bottom}${suffix} ${left}${suffix}`;
}

const zeroValue = [0, '0px'];

const cssPropertiesDefaultValues: Record<string, (string|number)[]> = {
    width: ['auto'],
    height: ['auto'],
    alignItems: ['normal', 'stretch'],
    alignSelf: ['auto', 'normal'],
    bottom: ['auto'],
    boxSizing: ['content-box'],
    flexBasis: ['auto'],
    flexDirection: ['row'],
    flexGrow: [0],
    flexShrink: [0],
    flexWrap: ['nowrap'],
    fontWeight: ['normal', 400],
    gap: zeroValue,
    justifyContent: ['flex-start'],
    left: ['auto'],
    letterSpacing: zeroValue,
    opacity: [1],
    overflow: ['visible'],
    position: ['static'],
    textAlign: ['left'],
    visibility: ['visible'],
    zIndex: ['auto'],
    paddingTop: zeroValue,
    paddingRight: zeroValue,
    paddingBottom: zeroValue,
    paddingLeft: zeroValue,
    marginLeft: zeroValue,
    marginTop: zeroValue,
};

/**
 * 删除冗余样式：
 * 1、值是默认值的 Style
 * 2、效果重复的 Style
 * @param styles NodeStyle
 * @param nodeType NodeType
 */
export function removeRedundancyStyle(styles: NodeStyle, nodeType: string) {
  const optimizedStyles: NodeStyle = {};

  for (const property in styles) {
    // 去除值是默认值的属性，或者没有值的属性
    if (
      !cssPropertiesDefaultValues.hasOwnProperty(property) ||
      (cssPropertiesDefaultValues.hasOwnProperty(property) &&
        styles[property] !== undefined &&
        !cssPropertiesDefaultValues[property].includes(styles[property]))
    ) {
      optimizedStyles[property] = styles[property];
    }
  }

  if (nodeType === 'TEXT') {
    delete optimizedStyles.flexGrow;
    delete optimizedStyles.flexDirection;
    delete optimizedStyles.flexBasis;
  }

  return optimizedStyles;
}

/**
 * REST API 拿到的单行文本多色值情况翻译为 Plugin API 风格
 * Mock 数据：
 * "characters": "abcdefjx",
 * "characterStyleOverrides": [
 *           1,
 *           1,
 *           1,
 *           2,
 *           2,
 *           2,
 *           2,
 *           2
 * ],
 *
 *         "styleOverrideTable": {
 *           "1": {
 *             "fills": [
 *               {
 *                 "blendMode": "NORMAL",
 *                 "type": "SOLID",
 *                 "color": {
 *                   "r": 0.8074055910110474,
 *                   "g": 0.027228912338614464,
 *                   "b": 0.027228912338614464,
 *                   "a": 1
 *                 }
 *               }
 *             ]
 *           },
 *           "2": {
 *             "fills": [
 *               {
 *                 "blendMode": "NORMAL",
 *                 "type": "SOLID",
 *                 "color": {
 *                   "r": 0.8509804010391235,
 *                   "g": 0.8509804010391235,
 *                   "b": 0.8509804010391235,
 *                   "a": 1
 *                 }
 *               }
 *             ],
 *             "inheritFillStyleId": "1:2"
 *           }
 *         }
 * @param node
 */
export function restTextSegmentsFills(node: TextNode): TextSegmentsFillItem[] {
  const result: TextSegmentsFillItem[] = [];
  const characterStyleOverrides: number[] = node.characterStyleOverrides;
  const styleOverrideTable = node.styleOverrideTable;
  const characters = node.characters;
  if (node.type === 'TEXT'
      && characterStyleOverrides?.length > 0
      && styleOverrideTable
      && characters
  ) {
      let charactersTemp = '';
      let start = 0;
      let end = 0;
      characterStyleOverrides.forEach((item, index) => {
          end = index;
          if (index === characterStyleOverrides.length - 1 || index !== 0 && item !== characterStyleOverrides[index - 1]) { // 起点重置
              let styleMapId: number;
              if (index === characterStyleOverrides.length - 1) { // 字符串最后一位
                  styleMapId = item;
                  charactersTemp += characters[index];
                  end = index + 1;
              } else {
                  styleMapId = characterStyleOverrides[index - 1];
              }
              const curStyleOverride = styleOverrideTable[styleMapId];

              // 将重置前的结果写入
              result.push({
                  styleMapId,
                  characters: charactersTemp,
                  start,
                  end,
                  styles: curStyleOverride || {}
              });
              charactersTemp = characters[index];
              start = index;
          } else {
              charactersTemp += characters[index];
          }
      })
  }
  return result;
}