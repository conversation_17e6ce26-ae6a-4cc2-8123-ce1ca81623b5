import { FrameNode } from '@figma/rest-api-spec';
import { IMAGE_NODE_TYPES } from '../const';
import type { FigmaNode } from '../types';


function onlyHasShape(node: FigmaNode): boolean {
  if ('children' in node && node.children.length > 0) {

    // RECTANGLE 和其它类型的 Shape 一起出现时，识别为图片
    return !node.children.some(child => {
      return !([...IMAGE_NODE_TYPES, 'RECTANGLE'].includes(child.type) || onlyHasShape(child) && child.type !== 'FRAME');
    });
  }

  return false;
}

/**
 * 是否图片节点
 * @param node
 */
export function isImage(node: FigmaNode): boolean {
  if (!node) {
    return false;
  }

  // 有导出设置
  if (((node as FrameNode).exportSettings?.length ?? 0) > 0 && ((node as FrameNode)?.children?.length ?? 0) === 0) {
    return true;
  }

  // 是否是 Shape 类型的 NodeType
  if (IMAGE_NODE_TYPES.includes(node.type)) {
    return true;
  }

  // 所有的子图层都是 Shape 类型
  if (onlyHasShape(node)) {
    return true;
  }

  // 填充图片的 FRAME 或者 RECTANGLE
  if (
    (node.type === 'FRAME' || node.type === 'RECTANGLE')
    && (Array.isArray(node.fills) && node.fills?.find(paint => paint.type === 'IMAGE' && paint.visible !== false) !== undefined)
  ) {
    return true;
  }

  // @ts-expect-error
  // 有子图层被标记为 mask 时
  if ('children' in node && node.children?.some(node => node.isMask)) {
    return true;
  }

  // 如果只有一个子元素，需要递归判断子元素是否是图片
  if ('children' in node && node?.children.length === 1) {
    return isImage(node.children[0]);
  }

  return false;
}

/**
 * 是否文本
 * @param node
 */
export function isText(node: FigmaNode): boolean {
  return node.type === 'TEXT';
}
