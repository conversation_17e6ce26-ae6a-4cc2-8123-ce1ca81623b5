import { Logger } from '@/util/log';
import { imageFetchConfig } from './types';
import { NodeDefinition } from './types';

const logger = new Logger('parse-figma-json');

/**
 * 并发控制信号量
 */
class Semaphore {
  private permits: number;
  private waitQueue: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(): Promise<void> {
    return new Promise((resolve) => {
      if (this.permits > 0) {
        this.permits--;
        resolve();
      } else {
        this.waitQueue.push(resolve);
      }
    });
  }

  release(): void {
    this.permits++;
    if (this.waitQueue.length > 0) {
      const next = this.waitQueue.shift()!;
      this.permits--;
      next();
    }
  }
}

/**
 * 重试包装器
 */
async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;

      // 最后一次重试失败或者非可重试错误
      if (i === maxRetries || !isRetriableError(error)) {
        throw lastError;
      }

      // 指数退避延迟
      const delay = baseDelay * Math.pow(2, i);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

/**
 * 判断是否为可重试的错误
 */
function isRetriableError(error: any): boolean {
  // 网络错误、超时、5xx 服务器错误可重试
  if (error.code === 'ECONNRESET' ||
    error.code === 'ETIMEDOUT' ||
    error.code === 'ENOTFOUND') {
    return true;
  }

  // HTTP 状态码判断
  if (error.response?.status) {
    const status = error.response.status;
    // 5xx 服务器错误和 429 限流可重试
    return status >= 500 || status === 429;
  }

  return false;
}

/**
 * 批量获取图片 URL
 */
async function fetchImageBatch(
  figmaClient: any,
  fileKey: string,
  nodeIds: string[],
  config: Required<imageFetchConfig>
): Promise<Record<string, string | null>> {
  if (nodeIds.length === 0) {
    return {};
  }

  const nodeIdsStr = nodeIds.join(',');

  try {
    const response = await figmaClient.getImages(
      fileKey,
      nodeIdsStr,
      config.scale || 1,
      config.format || 'png',
      {
        timeout: config.timeout
      }
    );

    // 处理响应数据
    const imageMap: Record<string, string | null> = {};

    if (response && response.images) {
      for (const nodeId of nodeIds) {
        imageMap[nodeId] = response.images[nodeId] || null;
      }
    } else {
      // 如果响应格式不符合预期，将所有节点标记为失败
      for (const nodeId of nodeIds) {
        imageMap[nodeId] = null;
      }
    }

    return imageMap;
  } catch (error) {
    // 返回失败映射
    const imageMap: Record<string, string | null> = {};
    for (const nodeId of nodeIds) {
      imageMap[nodeId] = null;
    }

    throw error; // 重新抛出以便重试逻辑处理
  }
}

/**
 * 更新节点的图片 URL
 */
function updateNodeImageUrls(node: NodeDefinition, imageUrlMap: Record<string, string | null>): void {
  function updateNode(currentNode: NodeDefinition) {
    if (currentNode.nodeType === 'image' && imageUrlMap.hasOwnProperty(currentNode.id)) {
      const imageUrl = imageUrlMap[currentNode.id];

      if (imageUrl) {
        currentNode.imageUrl = imageUrl;
      }
    }

    if (currentNode.children && currentNode.children.length > 0) {
      currentNode.children.forEach((child: NodeDefinition) => updateNode(child));
    }
  }

  updateNode(node);
}

/**
 * 获取并填充图片节点的 URL
 * @param rootNode 根节点
 * @param figmaClient Figma 客户端实例
 * @param fileKey 文件键
 * @param config 图片获取配置
 * @returns 更新后的根节点
 */
export async function fetchImageUrlForNodes(
  rootNode: NodeDefinition,
  imageNodes: string[],
  fileKey?: string,
  figmaClient?: any,
  config: imageFetchConfig = {}
): Promise<NodeDefinition> {
  // 设置默认配置
  const defaultConfig: Required<imageFetchConfig> = {
    fetchImage: true,
    maxConcurrent: 3,
    batchSize: 15,
    timeout: 10000,
    retryCount: 3,
    retryDelay: 1000,
    scale: 1,
    format: 'png'
  };

  const finalConfig = { ...defaultConfig, ...config };

  // 如果未启用图片获取，直接返回
  if (!finalConfig.fetchImage) {
    return rootNode;
  }

  // 检查必要参数
  if (!figmaClient || !fileKey) {
    throw new Error('[missing necessary parameters: figmaClient or fileKey');
  }

  if (imageNodes.length === 0) {
    return rootNode;
  }

  // 创建并发控制信号量
  const semaphore = new Semaphore(finalConfig.maxConcurrent);

  // 将图片节点分批处理
  const batches: string[][] = [];

  for (let i = 0; i < imageNodes.length; i += finalConfig.batchSize) {
    batches.push(imageNodes.slice(i, i + finalConfig.batchSize));
  }

  // 并发处理所有批次
  const batchPromises = batches.map(async (batch, index) => {
    await semaphore.acquire();

    try {
      const batchResult = await withRetry(
        () => fetchImageBatch(figmaClient, fileKey, batch, finalConfig),
        finalConfig.retryCount,
        finalConfig.retryDelay
      );
      return batchResult;

    } catch (error) {
      // 返回失败映射，但不阻断其他批次
      const failedMap: Record<string, string | null> = {};
      batch.forEach(nodeId => {
        failedMap[nodeId] = null;
      });

      return failedMap;
    } finally {
      semaphore.release();
    }
  });

  // 等待所有批次完成
  const batchResults = await Promise.all(batchPromises);

  // 合并所有批次的结果
  const allImageUrls: Record<string, string | null> = {};
  batchResults.forEach(batchResult => {
    Object.assign(allImageUrls, batchResult);
  });

  // 更新节点的图片 URL
  updateNodeImageUrls(rootNode, allImageUrls);

  return rootNode;
}
