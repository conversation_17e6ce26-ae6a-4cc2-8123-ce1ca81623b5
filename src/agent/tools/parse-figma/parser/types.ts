import {
  Node as FigmaMixNode,
  EmbedNode,
  LinkUnfurlNode,
  SectionNode,
  SliceNode,
  TableCellNode,
  WidgetNode,
  DocumentNode,
  CanvasNode,
  ComponentPropertyType,
  TypeStyle
} from '@figma/rest-api-spec';

export type FigmaNode = Exclude<
  FigmaMixNode,
  EmbedNode | LinkUnfurlNode | SectionNode | SliceNode | TableCellNode | WidgetNode | DocumentNode | CanvasNode
>;

export interface OneNodeWorkResult {
  node?: NodeDefinition;
}

export enum LAYOUT_TYPES {
  FlexHorizontal = 'FlexHorizontal',
  FlexVertical = 'FlexVertical',
  Relative = 'Relative',
  Absolute = 'Absolute'
}

export interface imageFetchConfig {
  /**
   * 是否获取图片节点的 URL
   */
  fetchImage?: boolean;

  /**
   * 并发请求数
   */
  maxConcurrent?: number;

  /**
   * 每批处理节点数
   */
  batchSize?: number;

  /**
   * 请求超时时间（毫秒）
   */
  timeout?: number;

  /**
   * 重试次数
   */
  retryCount?: number;

  /**
   * 重试延迟（毫秒）
   */
  retryDelay?: number;

  /**
   * 图片缩放比例
   */
  scale?: number;

  /**
   * 图片格式
   */
  format?: 'jpg' | 'png' | 'svg' | 'pdf';
}

export interface ConversionFigmaJson {
  name: string;
  lastModified: string;
  nodes?: NodeDefinition;
  components: Record<string, ComponentDefinition>;
  componentSets: Record<string, ComponentSetDefinition>;
}

export interface NodeDefinition {
  id: string; // 唯一标识
  parentId?: string; // 父节点标识
  nodeType: NodeType; // 节点类型 View、Image、Text、Component
  boundingRect: Rectangle; // 节点尺寸、坐标信息
  renderBoundingRect: Rectangle; // 渲染节点尺寸、坐标信息
  name: string; // 节点名称
  children: NodeDefinition[]; // 子节点
  styles: NodeStyle;
  componentId?: string;
  componentProperties?: ComponentProperties[];
  characters?: string | string[]; // TEXT 类型的内容，如果是数组，则表示是多行文本
  imageUrl?: string; // 图片 url
  layoutType?: LAYOUT_TYPES;
  zIndex?: number;
}

export interface ComponentProperties {
  name: string;
  value: string;
  type: ComponentPropertyType;
}

export type NodeStyle = Partial<CSSStyleDeclaration>;

export type PrimitiveValue = string | number | null | undefined | PrimitiveArray | PrimitiveObject;

export type PrimitiveArray = PrimitiveValue[];

export interface PrimitiveObject {
  [key: string]: PrimitiveValue;
}

export interface PlainObject {
  [name: string]: string;
}

export type NodeType = 'component' | 'view' | 'text' | 'image';

export interface Rectangle {
  x: number;
  y: number;
  width: number;
  height: number;
  left: number;
  top: number;
  bottom: number;
  right: number;
}

export interface ComponentDefinition {
  id: string;
  name: string;
  description?: string;
  componentSetId?: string;
}

export interface ComponentSetDefinition {
  id: string;
  name: string;
  description?: string;
}

export interface TextSegmentsFillItem {
  styleMapId: number;
  characters: string;
  start: number;
  end: number;
  styles: TypeStyle;
}
