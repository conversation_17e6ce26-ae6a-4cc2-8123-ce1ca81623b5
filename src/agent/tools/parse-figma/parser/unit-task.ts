import { Node as FigmaMixNode } from '@figma/rest-api-spec';
import { FigmaNode } from './types';

import { convertNode } from './node-parse';
import type { NodeDefinition, OneNodeWorkResult } from './types';
import type { TraverseContext } from './index';

/**
 * 转换任务单元 
 */
export async function doUnitTask(
  rootNode: FigmaMixNode, // 根节点
  parentNode: FigmaNode | undefined, // 父节点
  curNode: FigmaNode, // 当前节点
  traverseContext: TraverseContext, // 遍历上下文
  parentWorkResult: OneNodeWorkResult, // 上一次处理结果
  index: number | undefined, // 当前节点索引
) {

  const unitTaskWorkResult = await convertNode(
    rootNode,
    parentNode,
    curNode,
    traverseContext,
    parentWorkResult,
    index ?? 1,
  );

  return unitTaskWorkResult;
}
