import * as path from 'path';
// @ts-ignore-next-line
import pdf from 'pdf-parse/lib/pdf-parse';
import mammoth from 'mammoth';
import fs from 'fs/promises';
import { isBinaryFile } from 'isbinaryfile';

// 以 kwaipilot模型为限制，文件读取最大时 max_token
const MAX_CONTENT_LENGTH = 32000 * 4;
export type ExtractTextRes = { totalLineNum: number; content: string; readedLineNum: number };
export async function extractTextFromFile(
  filePath: string,
  options?: { startLine?: number; endLine?: number }
): Promise<ExtractTextRes> {
  try {
    await fs.access(filePath);
  } catch (error) {
    throw new Error(`File not found: ${filePath}`);
  }
  const fileExtension = path.extname(filePath).toLowerCase();

  // 将文件内容每行前加上当前行号
  const prefixLine = (text: string, startLine: number): string => {
    const lines = text.split('\n');
    return lines
      .map((line, index) => {
        const lineNumber = startLine + index;
        return `${lineNumber.toString()}\t${line}`;
      })
      .join('\n');
  };

  // 处理行范围参数
  const processLines = (text: string): ExtractTextRes => {
    const lines = text.split('\n');
    const truncatedText = text.slice(0, MAX_CONTENT_LENGTH);
    const truncatedLines = truncatedText.split('\n').length;
    if (!options?.startLine && !options?.endLine) {
      return {
        totalLineNum: lines.length,
        readedLineNum: text.length > MAX_CONTENT_LENGTH ? truncatedLines : lines.length,
        content: prefixLine(
          text.length > MAX_CONTENT_LENGTH
            ? truncatedText +
                `\n\n\n[Note: The content was truncated by line ${truncatedLines} due to the content is too long (${lines.length} lines total). If you need more information, you may read a range of lines starting from line ${truncatedLines}.]`
            : text,
          1
        )
      };
    }
    const start = options?.startLine ? Math.max(0, options.startLine - 1) : 0;
    const end = options?.endLine ? Math.min(lines.length, options.endLine) : lines.length;
    // 尾部添加文件总行数
    return {
      totalLineNum: lines.length,
      readedLineNum: end - start,
      content:
        prefixLine(lines.slice(start, end).join('\n'), start + 1) +
        `\n\n[Note: There are a total of ${lines.length} lines in the file.]`
    };
  };

  switch (fileExtension) {
    case '.pdf':
      return processLines(await extractTextFromPDF(filePath));
    case '.docx':
      return processLines(await extractTextFromDOCX(filePath));
    case '.ipynb':
      return processLines(await extractTextFromIPYNB(filePath));
    default:
      const isBinary = await isBinaryFile(filePath).catch(() => false);
      if (!isBinary) {
        return processLines(await fs.readFile(filePath, 'utf8'));
      } else {
        throw new Error(`Cannot read text for file type: ${fileExtension}`);
      }
  }
}

async function extractTextFromPDF(filePath: string): Promise<string> {
  const dataBuffer = await fs.readFile(filePath);
  const data = await pdf(dataBuffer);
  return data.text;
}

async function extractTextFromDOCX(filePath: string): Promise<string> {
  const result = await mammoth.extractRawText({ path: filePath });
  return result.value;
}

async function extractTextFromIPYNB(filePath: string): Promise<string> {
  const data = await fs.readFile(filePath, 'utf8');
  const notebook = JSON.parse(data);
  let extractedText = '';

  for (const cell of notebook.cells) {
    if ((cell.cell_type === 'markdown' || cell.cell_type === 'code') && cell.source) {
      extractedText += cell.source.join('\n') + '\n';
    }
  }

  return extractedText;
}
