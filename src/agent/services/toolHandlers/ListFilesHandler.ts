import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHand<PERSON> } from '../ToolHelpers';
import { TextBlockParamVersion1, SayTool } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import path from 'path';
import { getReadablePath } from '@/util/path';
import { listFiles } from '../../tools/list-files';
import { formatResponse } from '../../prompt/responses';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * 列出文件工具处理器
 */
export class ListFilesHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {
  }

  async handle(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[]
  ): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const relDirPath: string | undefined = block.params.path;
    const recursiveRaw: string | boolean | undefined = block.params.recursive;
    // 处理 recursive 参数：可能是布尔值或字符串
    const recursive = typeof recursiveRaw === 'boolean' 
      ? recursiveRaw 
      : recursiveRaw?.toLowerCase() === 'true';
    const sharedMessageProps: SayTool = {
      tool: !recursive ? 'listFilesTopLevel' : 'listFilesRecursive',
      path: getReadablePath(this.context.cwd, ToolHelpers.removeClosingTag('path', relDirPath, block.partial))
    };
    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: ''
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);

        return;
      } else {
        if (!relDirPath) {
          this.context.stateManager.updateState({ consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1 });
          ToolHelpers.pushToolResult(block, userMessageContent, await ToolHelpers.sayAndCreateMissingParamError('list_files', 'path', this.context), this.context.stateManager);

          return;
        }
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'list_files'
        });
        const absolutePath = path.resolve(this.context.cwd, relDirPath);

        const toolLog = ToolHelpers.generateToolLog('list_files', this.context.loggerManager);
        toolLog.start(`${absolutePath} ${recursive}`);
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            path: absolutePath,
            recursive
          },
          metadata: {
            name: block.name
          }
        });
        const startToolTime = Date.now();
        const [files, didHitLimit] = await listFiles(absolutePath, recursive, 200);
        toolLog.end(JSON.stringify(files));
        generationCall?.end({
          output: { files, didHitLimit }
        });

        const result = formatResponse.formatFilesList(absolutePath, files, didHitLimit);
        const completeMessage = JSON.stringify({
          ...sharedMessageProps,
          content: result
        } satisfies SayTool);
        await this.context.messageService.say('tool', completeMessage, false);
        ToolHelpers.pushToolResult(block, userMessageContent, result, this.context.stateManager);
        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });
        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          path: absolutePath,
          recursive,
          didHitLimit
        });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(block, userMessageContent, this.context, generationCall, 'listing files', error, 'list_files');
    }
  }
} 