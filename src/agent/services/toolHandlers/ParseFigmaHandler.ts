import { ToolUse } from '../../types/message';
import { <PERSON>lHelpers, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1, SayTool } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { convertFigmaJson } from '../../tools/parse-figma';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * 解析 Figma 工具处理器
 */
export class ParseFigmaHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const url: string | undefined = block.params.url;
    const sharedMessageProps: SayTool = {
      tool: 'parseFigma',
      url: ToolHelpers.removeClosingTag('url', url)
    };

    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: ''
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        if (!url) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          const errorMessage = await ToolHelpers.sayAndCreateMissingParamError('parse_figma', 'url', this.context);
          ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
          return;
        }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });

        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'parse_figma'
        });

        const toolLog = ToolHelpers.generateToolLog('parse_figma', this.context.loggerManager);
        toolLog.start(url);

        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            url
          },
          metadata: {
            name: block.name
          }
        });

        const startToolTime = Date.now();

        // 解析 Figma Node JSON
        const result = await convertFigmaJson(url, {
          getTokenFn: () => this.getEnvironmentFigmaToken(),
          imageConfig: {
            fetchImage: true
          }
        });

        toolLog.end(result);
        generationCall?.end({
          output: { result }
        });

        const successMessage = JSON.stringify({
          ...sharedMessageProps,
          content: result
        } satisfies SayTool);

        ToolHelpers.pushToolResult(block, userMessageContent, successMessage, this.context.stateManager);

        await this.context.messageService.say('tool', successMessage, false);

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          url
        });
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'parsing figma node',
        error,
        'parse_figma'
      );
    }
  }

  /**
   * 获取 Figma Token
   */
  private async getEnvironmentFigmaToken(): Promise<string> {
    const res = await this.context.messenger.request('assistant/agent/figmaToken', {});
    return res.data || '';
  }
}
