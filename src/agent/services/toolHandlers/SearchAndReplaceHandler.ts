import { ToolUse, ToolUseName } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1 } from '../../types/type.d';
import { SayTool } from '../../types/type';
import { getReadablePath } from '@/util/path';
import { fileExistsAtPath } from '@/util/fs';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { constructNewFileContent } from '../../utils/diff';
import path from 'path';
import fs from 'fs/promises';
import delay from 'delay';
import { LangfuseGenerationClient } from 'langfuse';
import { getFileLanguage } from '@/util/fileType';

/**
 * 搜索替换工具处理器
 */
export class SearchAndReplaceHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    // Extract and validate parameters
    const relPath: string | undefined = block.params.path;
    let diff: string | undefined = block.params.diff; // for replace_in_file

    try {
      const sharedMessageProps: SayTool = {
        tool: 'editFile',
        path: getReadablePath(this.context.cwd, relPath),
        content: '',
        tool_version: 'v2'
      };

      // Handle partial tool use
      if (block.partial) {
        await this.context.messageService
          .say('tool', JSON.stringify(sharedMessageProps), block.partial)
          .catch(() => {});
        return;
      }

      // Validate required parameters
      if (!relPath) {
        this.context.stateManager.updateState({
          consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
        });
        const errorMessage = await ToolHelpers.sayAndCreateMissingParamError('replace_in_file', 'path', this.context);
        ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
        return;
      }

      // Validate replacements array if provided
      if (!diff) {
        this.context.stateManager.updateState({
          consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
        });
        const errorMessage = await ToolHelpers.sayAndCreateMissingParamError('replace_in_file', 'diff', this.context);
        ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
        return;
      }

      // At this point we know relPath is defined
      const validRelPath = relPath as string;
      const absolutePath = path.resolve(this.context.cwd, validRelPath);
      const startToolTime = Date.now();
      const fileExists = await fileExistsAtPath(absolutePath);

      if (!fileExists) {
        this.context.stateManager.updateState({
          consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
        });
        const formattedError = `File does not exist at path: ${absolutePath}\nThe specified file could not be found. Please verify the file path and try again.`;
        await this.context.messageService.say('error', formattedError);
        ToolHelpers.pushToolResult(block, userMessageContent, formattedError, this.context.stateManager);
        return;
      }

      // Reset consecutive mistakes since all validations passed
      this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });

      // Read and process file content
      let fileContent: string;
      try {
        fileContent = await fs.readFile(absolutePath, 'utf-8');
      } catch (error) {
        this.context.stateManager.updateState({
          consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
        });
        const errorMessage = `Error reading file: ${absolutePath}\nFailed to read the file content: ${
          error instanceof Error ? error.message : String(error)
        }\nPlease verify file permissions and try again.`;
        await this.context.messageService.say('error', errorMessage);
        ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
        return;
      }

      this.context.loggerManager.reportUserAction({
        key: 'agent_tools_request',
        type: 'replace_in_file',
        content: JSON.stringify({
          toolName: block.name,
          sessionId: this.context.stateManager.getState().sessionId,
          chatId: this.context.stateManager.getState().chatId,
          params: {
            path: absolutePath,
            fileType: getFileLanguage(absolutePath),
            lines: fileContent.split('\n').length
          }
        })
      });

      const result = await constructNewFileContent(diff, fileContent || '');
      const newContent = result.content;
      const replacements = result.replacements;

      // Report code generation
      if (this.context.agentManager) {
        await this.context.agentManager.reportGenerateCode(
          replacements.map((replacement) => ({ ...replacement, filePath: validRelPath }))
        );
      }

      await this.context.messageService.say(
        'tool',
        JSON.stringify({ ...sharedMessageProps, content: newContent, diff }),
        block.partial
      );
      await delay(1000); // wait for diff view to update

      generationCall = this.context.loggerManager.getTrace()?.generation({
        name: 'tool_call',
        input: {
          path: validRelPath,
          diff,
          newContent,
          replacements
        },
        metadata: {
          name: block.name
        }
      });

      if (!this.context.messenger) {
        throw new Error('Messenger not available for writeToFile');
      }

      const { data } = await this.context.messenger.request('assistant/agent/writeToFile', {
        path: validRelPath,
        content: newContent,
        newFile: false,
        sessionId: this.context.stateManager.getState().sessionId,
        chatId: this.context.stateManager.getState().chatId
      });

      this.context.messageService.say('edit_file_result', JSON.stringify(data));

      // 计算replaceLines之和
      const replaceLines = replacements.reduce((sum, i) => sum + i.replace.length, 0);

      ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
        contentLength: newContent.length,
        noModified: !!data?.noModified,
        lines: fileContent.split('\n').length,
        type: data?.type,
        path: absolutePath,
        fileType: getFileLanguage(absolutePath),
        replaceLines
      });

      if (data?.noModified) {
        ToolHelpers.pushToolResult(
          block,
          userMessageContent,
          `No changes needed for '${relPath}'`,
          this.context.stateManager
        );
        return;
      }

      if (data?.type === 'success') {
        const resultMessage = [
          `The updated content has been successfully saved to ${validRelPath}.\n\n`,
          `Please note:\n`,
          `1. You do not need to re-write the file with these changes, as they have already been applied.\n`,
          `2. Proceed with the task using the updated file content as the new baseline.\n`,
          `3. If the user's edits have addressed part of the task or changed the requirements, adjust your approach accordingly.`
        ].join('');
        ToolHelpers.pushToolResult(block, userMessageContent, resultMessage, this.context.stateManager);
      } else {
        await ToolHelpers.handleError(
          block,
          userMessageContent,
          this.context,
          null,
          'replace in file',
          new Error(data?.content || ''),
          'replace_in_file'
        );
      }

      generationCall?.end({
        output: { type: data?.type, content: data?.content }
      });

      this.context.loggerManager.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-chat-tool',
        millis: Date.now() - startToolTime,
        extra4: data?.type === 'success' ? 'success' : 'error',
        extra6: block.name
      });

      // 保存检查点
      await this.context.checkpointService?.saveCheckpoint();
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'replace in file',
        error,
        'replace_in_file'
      );
    }
  }
}
