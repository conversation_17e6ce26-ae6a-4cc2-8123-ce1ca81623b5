import { ToolUse } from '../types/message';
import { StateManager } from './StateManager';
import { MessageService } from './MessageService';
import { TextBlockParamVersion1 } from '../types/type.d';
import { IMessenger } from '@/protocol/messenger';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { ToolHelpers, ToolHandlerContext } from './ToolHelpers';
import { TOOL_HANDLERS, ToolName } from './toolHandlers';
import { LoggerManager } from './LoggerManager';
import { CheckpointService } from './CheckpointService';
import { getAvailableMcpServers } from '../tools/mcp-tool';

/**
 * 工具处理器统一入口类 - 使用分散的单个处理器
 */
class ToolHandlers {
  private context: ToolHandlerContext;
  private handlerMap: Record<string, any>;

  constructor(context: ToolHandlerContext) {
    this.context = context;
    // 使用 TOOL_HANDLERS 映射来创建处理器实例
    this.handlerMap = Object.fromEntries(
      Object.entries(TOOL_HANDLERS).map(([key, HandlerClass]) => [key, new HandlerClass(context)])
    );
  }

  // 通用处理器方法 - 通过工具名称委托调用
  async handleTool(toolName: string, block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    const handler = this.handlerMap[toolName];
    if (handler) {
      await handler.handle(block, userMessageContent);
    } else {
      ToolHelpers.pushToolResult(
        block,
        userMessageContent,
        `Tool ${toolName} not implemented`,
        this.context.stateManager
      );
    }
  }
}

/**
 * 工具执行器类 - 负责工具使用的逻辑处理
 */
export class ToolExecutor {
  constructor(
    private messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    private sessionInfo: any,
    private cwd: string,
    private stateManager: StateManager,
    private messageService: MessageService,
    private checkpointService: CheckpointService,
    private loggerManager: LoggerManager,
    private agentManager?: any // 添加 agentManager 参数
  ) {
    // 构造函数现在更简洁，不需要额外的logger实例
  }

  /**
   * 执行工具使用
   */
  async executeToolUse(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    const state = this.stateManager.getState();
    const mcpServers = getAvailableMcpServers(this.loggerManager.getAgentLogger());
    /**
     * 模型在调用工具时返回的工具名称可能和传入的工具名称不一致，模型对工具名称大小写不敏感
     * 因此在其名字时如果是两单词拼接在一起时最好使用_链接
     */
    const server = mcpServers.find((server) => server.tools?.find((tool) => tool.name.toLowerCase() === block.name.toLowerCase()));

    if (server) {
      block.params.arguments = JSON.stringify(block.params);
      block.params.server_name = server.name;
      block.params.tool_name = block.name;
      block.name = 'use_mcp_tool';
    }

    // 检查工具是否被启用
    const toolSwitchManager = this.stateManager.toolSwitchManager;
    if (toolSwitchManager && !toolSwitchManager.isToolEnabled(block.name)) {
      const message = `工具 ${block.name} 已被禁用，无法执行。`;
      this.loggerManager.agentInfo(message);
      ToolHelpers.pushToolResult(block, userMessageContent, message, this.stateManager);
      return;
    }

    // 创建工具处理器上下文
    const handlerContext: ToolHandlerContext = {
      stateManager: this.stateManager,
      messageService: this.messageService,
      loggerManager: this.loggerManager,
      cwd: this.cwd,
      sessionInfo: this.sessionInfo,
      messenger: this.messenger,
      checkpointService: this.checkpointService,
      agentManager: this.agentManager
    };

    // 创建工具处理器并直接执行工具
    const toolHandlers = new ToolHandlers(handlerContext);
    await toolHandlers.handleTool(block.name, block, userMessageContent);
  }
}
