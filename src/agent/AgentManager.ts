import {
  <PERSON><PERSON>,
  ImageBlockParam,
  MessageParam,
  TextBlockParamVersion1,
  ToolUse,
  WebviewMessage
} from '@/agent/types/type';
import { IMessenger } from '@/protocol/messenger';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { StateManager } from './services/StateManager';
import { StreamManager } from './services/StreamManager';
import { MessageService } from './services/MessageService';
import { CheckpointService } from './services/CheckpointService';
import { ToolExecutor } from './services/ToolExecutor';
import { ToolHelpers } from './services/ToolHelpers';
import { LoggerManager } from './services/LoggerManager';
import { ToolSwitchState } from './utils/toolSwitches';
import { SYSTEM_PROMPT } from './prompt/system';
import { getAvailableMcpServers } from './tools/mcp-tool';
import { GlobalConfig } from '@/util/global';
import { TokenCalculator } from './context/TokenCalculator';
import { ContextManager } from './context/ContextManager';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { formatResponse } from './prompt/responses';
import { Api } from '@/http';
import { fetchModelConfig, fetchGrayConfig, checkEnableRepoIndex } from './utils/getConfig';
import { formatAssistantMessage, formatLlmMessages, parseAssistantMessage } from './tools/parse-assistant-message';
import { EventSourceMessage } from '@fortaine/fetch-event-source';
import { isAllMessagesVersion1 } from './utils/message';
import pWaitFor from 'p-wait-for';
import { v4 } from 'uuid';
import cloneDeep from 'clone-deep';
import { ReportGenerateCodeRequest } from './types/tool';
import { getCommonToolsFunctionSchemas } from './prompt/tools/schema';
import { getRulesListForAgent } from './rules';
import { listFiles } from './tools/list-files';
import { ErrorUtils } from '@/util/error';
import i18n from '@/i18n';

type UserContent = (TextBlockParamVersion1 | ImageBlockParam)[];
type AskResponse = 'yesButtonClicked' | 'noButtonClicked' | 'messageResponse';

export abstract class BaseAgentManager {
  protected stateManager: StateManager;
  protected streamManager: StreamManager;
  protected messageService: MessageService;
  protected checkpointService: CheckpointService;
  protected toolExecutor: ToolExecutor;
  // 日志管理器
  protected loggerManager: LoggerManager;
  // 中止控制器
  protected abortController?: AbortController;
  // 上下文管理器
  protected contextManager?: ContextManager;
  protected httpClient = new Api();

  constructor(
    protected messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    protected cwd: string,
    protected options: {
      model: string;
      maxTokens: number;
      isEnableRepoIndex: boolean;
      isUseNewTool: boolean;
      toolSwitchState?: ToolSwitchState;
      isSupportToolUse: boolean;
      isAskMode?: boolean;
    },
    protected sessionInfo?: WebviewMessage<'newTask'>
  ) {
    const sessionId = this.sessionInfo?.reqData.sessionId || '';
    const chatId = this.sessionInfo?.reqData.chatId || '';
    // 初始化日志管理器
    this.loggerManager = new LoggerManager({
      sessionId,
      chatId,
      username: this.sessionInfo?.reqData.username,
      scope: 'assistant-agent'
    });
    const messages = this.sessionInfo?.reqData.messages;
    const userMessages = messages?.filter((m) => m.role === 'user');
    const haveXMLToolUser = userMessages?.some(
      (m) =>
        Array.isArray(m.content) &&
        m.content.some(
          (c) =>
            'category' in c &&
            (c.category === 'tool-response' ||
              c.category === 'tool-title' ||
              c.category === 'tool-feedback' ||
              c.category === 'tool-exception')
        ) &&
        !m.content.some((c) => 'toolId' in c && c.toolId)
    );
    const isSupportToolUse = this.options.isSupportToolUse ? (haveXMLToolUser ? false : true) : false;

    this.loggerManager.agentInfo(`haveXMLToolUser: ${haveXMLToolUser}`);
    this.loggerManager.agentInfo(`传入的supportToolUse: ${this.options.isSupportToolUse}`);
    this.loggerManager.agentInfo(`最终计算的isSupportToolUse: ${isSupportToolUse}`);

    this.stateManager = new StateManager({
      sessionId,
      chatId,
      cwd: this.cwd,
      startTaskTime: Date.now(),
      modelConfig: {
        model: this.options.model,
        maxTokens: this.options.maxTokens,
        isSupportToolUse: isSupportToolUse
      },
      toolSwitchState: this.options.toolSwitchState,
      chatMode: this.options.isAskMode ? 'ask' :'agent',
    });
    const servers = getAvailableMcpServers(this.loggerManager.getAgentLogger());
    const enabledTools = this.stateManager.getEnabledTools();
    const systemPrompt = SYSTEM_PROMPT(
      this.stateManager.modelConfig.model,
      this.cwd,
      servers,
      this.options.isEnableRepoIndex,
      this.sessionInfo?.rules,
      '',
      this.options.isUseNewTool,
      this.options.isAskMode,
      enabledTools,
      this.sessionInfo?.reqData.deviceInfo?.ide
    );
    this.stateManager.updateState({
      systemPrompt
    });
    // 初始化服务
    this.streamManager = new StreamManager(this.stateManager);
    this.messageService = new MessageService(this.messenger, this.stateManager, this.loggerManager);
    this.checkpointService = new CheckpointService(this.stateManager, this.messageService, this.loggerManager);
    // 初始化工具执行器
    this.toolExecutor = new ToolExecutor(
      this.messenger,
      this.sessionInfo,
      this.cwd,
      this.stateManager,
      this.messageService,
      this.checkpointService,
      this.loggerManager,
      this // 传递 agentManager 实例
    );
    // 初始化上下文管理器
    this.initializeContextManager();
    this.loggerManager.logTaskStart({
      sessionId: this.stateManager.sessionId,
      chatId: this.stateManager.chatId,
      ts: this.stateManager.startTaskTime,
      enableRepoIndex: this.options.isEnableRepoIndex,
      task: this.sessionInfo?.task,
      mcpServers: servers
    });
    this.loggerManager.agentInfo('系统提示词:', this.stateManager.systemPrompt);
  }

  // 公共配置获取逻辑
  protected static async getCommonConfig(
    sessionInfo?: WebviewMessage<'newTask'>
  ): Promise<{
    model: string;
    maxTokens: number;
    isUseNewTool: boolean;
    isEnableRepoIndex: boolean;
    isSupportToolUse: boolean;
  }> {
    let model = 'Kwaipilot Pro';
    let maxTokens = 30000;
    let isUseNewTool = false;
    let isEnableRepoIndex = true;
    let isSupportToolUse = false;
    try {
      const username = GlobalConfig.getConfig().getUsername();
      // 并行执行三个配置请求
      const [modelConfig, isUseNewToolValue, isEnableRepoIndexValue] = await Promise.all([
        fetchModelConfig({
          username,
          platform: sessionInfo?.reqData.deviceInfo?.platform || '',
          config: sessionInfo?.reqData.modelConfig
        }),
        fetchGrayConfig({ grayKey: 'replaceFileTool', username }),
        checkEnableRepoIndex()
      ]);

      model = modelConfig.model;
      maxTokens = modelConfig.maxTokens;
      isSupportToolUse = modelConfig.supportToolUse;
      isUseNewTool = isUseNewToolValue;
      isEnableRepoIndex = isEnableRepoIndexValue;
    } catch (e) { 
      // 使用默认配置
    }

    return {
      model,
      maxTokens,
      isUseNewTool,
      isEnableRepoIndex,
      isSupportToolUse
    };
  }

  // 异步 constructor，保证前期配置一定加载完成
  static async init(
    messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    cwd: string,
    sessionInfo?: WebviewMessage<'newTask'>,
    toolSwitchState?: ToolSwitchState,
    isAskMode?: boolean,
  ) {
    const config = await this.getCommonConfig(sessionInfo);
    
    return new StandardAgentManager(
      messenger,
      cwd,
      {
        ...config,
        toolSwitchState,
        isAskMode
      },
      sessionInfo
    );
  }

  /**
   * 开始任务
   */
  async startTask(): Promise<void> {
    // 初始化状态
    this.stateManager.updateState({ didCompleteTask: false });
    this.messageService.setLocalMessages(this.sessionInfo?.localMessages || []);
    const apiHistory: MessageParam[] = (this.sessionInfo?.reqData.messages || []).map((m) => ({
      version: 0,
      ...m
    }));
    this.messageService.setApiConversationHistory(apiHistory);
    const task = this.sessionInfo?.task;
    // 带@相关信息的task
    const taskForLlm = this.sessionInfo?.taskForLlm || task || '';
    const images: ImageBlockParam[] = (this.sessionInfo?.images || []).map((item) => ({
      type: 'image',
      source: {
        type: 'url',
        url: item
      }
    }));
    // 发送第一条任务消息
    await this.messageService.say('text', taskForLlm, undefined, 'user');
    // 开始任务循环
    await this.handleTaskLoop(
      [
        {
          type: 'text',
          text: `<task>\n${taskForLlm}\n</task>`,
          category: 'user-input'
        },
        ...images
      ],
      true
    );
  }

  /**
   * 停止任务
   */
  async stop(): Promise<void> {
    this.abortController?.abort();
    const handles = this.stateManager.getState().tasksToBeHandled;
    for (const handle of handles) {
      await handle();
    }
    this.stateManager.updateState({ abort: true });
    // 用户手动停止任务
    this.loggerManager.endGenerationWithMessage('用户手动停止任务');
    this.loggerManager.agentInfo('用户手动停止任务');
    this.loggerManager.reportAgentTask('agent_task_end_stop', {
      sessionId: this.stateManager.sessionId,
      chatId: this.stateManager.chatId,
      ts: Date.now(),
      duration: Date.now() - this.stateManager.getState().startTaskTime,
      modelName: this.stateManager.modelConfig.model
    });
  }

  /**
   * 更新询问响应
   */
  updateAskResponse(response: { askResponse: AskResponse; text?: string }): void {
    this.messageService.updateAskResponse(response);
  }

  /**
   * 初始化上下文管理器 - 可被子类重写
   */
  protected initializeContextManager() {
    // 使用当前模型配置初始化TokenCalculator
    const tokenCalculator = new TokenCalculator(this.stateManager.modelConfig.model);
    this.contextManager = new ContextManager(
      this.stateManager.modelConfig.maxTokens,
      this.stateManager.systemPrompt,
      tokenCalculator
    );
  }

  private async handleTaskLoop(userContent: UserContent, isNewTask: boolean): Promise<void> {
    let nextUserContent = userContent;
    let includeFileDetails = true;
    this.loggerManager.initTrace({
      sessionId: this.stateManager.sessionId,
      metadata: {
        ...this.sessionInfo?.reqData,
        systemPrompt: this.stateManager.systemPrompt
      },
      input: userContent
    });
    // 初始化langfuse
    this.checkpointService.setTrace(this.loggerManager.getTrace());
    // 初始化检查点
    await this.checkpointService.initCheckpointTracker();
    await this.checkpointService.saveCheckpoint();
    while (!this.stateManager.getState().abort) {
      const didEndLoop = await this.recursivelyMakeLLMRequests(nextUserContent, includeFileDetails, isNewTask);
      includeFileDetails = false;
      if (didEndLoop) {
        break;
      }
    }
    // 清理资源
    this.loggerManager.cleanup();
  }

  private async recursivelyMakeLLMRequests(
    userContent: UserContent,
    includeFileDetails: boolean = false,
    isNewTask: boolean = false
  ): Promise<boolean> {
    this.stateManager.updateState({ startLlmTime: Date.now() });
    if (this.stateManager.getState().abort) throw new Error('Kwaipilot instance aborted');
    // 检查任务是否已完成，如果任务已完成，直接返回true结束循环
    if (this.stateManager.getState().didCompleteTask) return true;

    await this.handleApiRequestLimits(userContent);
    await this.messageService.say('api_req_started', JSON.stringify(userContent));
    this.loggerManager.agentInfo(`废弃旧的abortController，创建新的abortController`);
    this.abortController?.abort();
    this.abortController = new AbortController();
    const innerAbortController = this.abortController;
    // 添加环境信息
    const environmentDetails = await this.messageService.getEnvironmentDetails(includeFileDetails);
    userContent.unshift({ type: 'text', text: environmentDetails, category: 'environment-details' });
    await this.messageService.addToApiConversationHistory({
      role: 'user',
      content: userContent,
      chatId: this.stateManager.getState().chatId
    });

    this.stateManager.resetStreamingState();
    this.loggerManager.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-pre-llm',
      millis: Date.now() - this.stateManager.getState().startLlmTime
    });
    try {
      const state = this.stateManager.getState();
      const isSupportToolUse = state.modelConfig.isSupportToolUse;
      const servers = getAvailableMcpServers(this.loggerManager.getAgentLogger());
      const enabledTools = this.stateManager.getEnabledTools();

      const messages =
        isAllMessagesVersion1(state.apiConversationHistory) && this.contextManager
          ? (
            await this.contextManager.optimizeMessagesContext(
              state.apiConversationHistory,
              this.loggerManager.getTrace()
            )
          ).messages
          : state.apiConversationHistory;

      const formattedMessages = isSupportToolUse
        ? formatLlmMessages(messages)
        : messages.map((m) => ({
          content: m.content,
          role: m.role,
          chatId: m.chatId
        }));
      const { userRules, projectRules } = getRulesListForAgent(this.sessionInfo?.rules || []);
      const reqParams: Chat.AgentChatRequest = {
        ...(this.sessionInfo?.reqData || {}),
        sessionId: state.sessionId,
        chatId: state.chatId,
        model: state.modelConfig.model,
        tools: isSupportToolUse
          ? getCommonToolsFunctionSchemas(
            this.cwd,
            servers,
            this.options.isEnableRepoIndex,
            this.options.isUseNewTool,
            enabledTools
          )
          : undefined,
        messages: formattedMessages,
        mode: isSupportToolUse ? (this.options.isAskMode ? 'ask' : 'agent') : undefined,
        rules: isSupportToolUse
          ? [
            {
              type: Chat.RuleType.USER_RULES,
              content: userRules.filter((rule) => rule).join('\n')
            },
            {
              type: Chat.RuleType.PROJECT_RULES,
              content: projectRules.filter((rule) => rule).join('\n')
            }
          ]
          : undefined,
        systemPrompt: isSupportToolUse ? undefined : state.systemPrompt,
        round: 0,
        environment: isSupportToolUse
          ? {
            ...(await this.messageService.getEnvironmentDetailsV2()),
            workingDirectory: this.cwd,
            workingDirectoryFiles: (await listFiles(this.cwd, false, 100))[0] || []
          }
          : undefined
      };
      let assistantMessage = '';
      this.stateManager.updateState({ isStreaming: true, stopReason: '' });
      this.loggerManager.agentInfo(`历史对话记录messages: ${JSON.stringify(messages)}`);
      const reqUUID = v4();
      // 创建一个 Promise 来追踪所有消息处理
      const messageProcessingPromise = new Promise<void>((resolve, reject) => {
        let isComplete = false;
        let hasError = false;
        let output = '';
        let returnFirstToken = false;
        this.loggerManager.logApiStart(reqUUID, state.modelConfig.model, reqParams);
        const startApiTime = Date.now();

        const baseHeaders = {
          'Content-Type': 'application/json;charset=UTF-8',
          'kwaipilot-username': this.sessionInfo?.reqData.username || '',
          'kwaipilot-platform': this.sessionInfo?.reqData.deviceInfo?.platform || ''
        }
        const headers: Record<string, string> = this.sessionInfo?.reqData.jwtToken ? {
          'Authorization': `Bearer ${this.sessionInfo?.reqData.jwtToken}`,
          ...baseHeaders
        } : {
          ...baseHeaders
        };


        const chatUrl = isSupportToolUse
          ? '/eapi/kwaipilot/plugin/composer/v2/chat/completions'
          : '/eapi/kwaipilot/plugin/composer/chat/completions';

        this.httpClient
          .fetchEventSource(chatUrl, {
            method: 'POST',
            body: JSON.stringify(reqParams),
            headers,
            signal: innerAbortController?.signal,
            onclose: () => {
              this.loggerManager.logApiEnd(reqUUID, state.modelConfig.model, Date.now() - startApiTime);
              if (innerAbortController?.signal.aborted) {
                this.loggerManager.agentInfoWithRequestId(reqUUID, '模型请求接口关闭，fetchEventSource aborted');
                this.loggerManager.endGenerationWithMessage('模型请求接口关闭，fetchEventSource aborted');
                return;
              }
              if (!isComplete) {
                resolve();
              }
            },
            onmessage: async (event: EventSourceMessage) => {
              if (!returnFirstToken) {
                this.loggerManager.logFirstToken(reqUUID, state.modelConfig.model, Date.now() - startApiTime);
                returnFirstToken = true;
              }
              if (innerAbortController?.signal.aborted) {
                this.loggerManager.agentInfoWithRequestId(reqUUID, 'fetchEventSource aborted');
                return;
              }
              const data = JSON.parse(event.data);
              const traceId = data.traceId;
              if (data.code === 413) {
                this.stateManager.updateState({
                  isTooLongForApiReq: true,
                  tooLongTip: data.tip
                });
                this.loggerManager.logApiTooLongError(
                  reqUUID,
                  state.modelConfig.model,
                  Date.now() - state.startLlmTime
                );
                return;
              }
              if (data.code === 4131) {
                this.stateManager.updateState({ stopReason: 'result_token_isTooLong' });
                this.loggerManager.agentInfo('回复token超长：', { code: data.code });
                return;
              }
              const isSupportToolUse = this.stateManager.getState().modelConfig.isSupportToolUse;

              const usage = isSupportToolUse ? data?.data?.usage : data?.usage;

              if (usage) {
                this.loggerManager.reportAgentTask('agent_task_token', {
                  requestId: reqUUID,
                  sessionId: this.stateManager.getState().sessionId,
                  chatId: this.stateManager.getState().chatId,
                  ts: Date.now(),
                  duration: Date.now() - this.stateManager.getState().startLlmTime,
                  modelName: this.stateManager.modelConfig.model,
                  usage: usage
                });
              }

              try {
                let delta = '';

                /** xml格式 工具调用 */
                if (!isSupportToolUse) {
                  delta = data?.message?.content;
                } else {
                  /** json schema格式 工具调用 */
                  const content = data?.data?.choices?.[0]?.message?.content;
                  if (content && Array.isArray(content)) {
                    for (let item of content) {
                      if (item.type === 'text') {
                        delta += item.text;
                      } else if (item.type === 'image') {
                        delta += item?.source?.url;
                      }
                    }
                  } else if (typeof content === 'string') {
                    delta += content;
                  }
                }

                assistantMessage += delta || '';
                output = assistantMessage;
                this.loggerManager.agentDebug(`流式返回信息 chunk: ${JSON.stringify(data)}`);
                this.loggerManager.agentInfoWithTrace(reqUUID, traceId, `流式返回信息元: ${delta}`);
                this.loggerManager.agentDebug(`流式返回信息（增量）: ${assistantMessage}`);
                const currentState = this.stateManager.getState();
                const prevLength = currentState.assistantMessageContent.length;

                const newAssistantMessageContent = isSupportToolUse
                  ? formatAssistantMessage(
                    data?.data?.choices?.[0]?.message,
                    this.stateManager.getState().assistantMessageContent
                  )
                  : parseAssistantMessage(assistantMessage);
                this.stateManager.updateState({ assistantMessageContent: newAssistantMessageContent });

                if (newAssistantMessageContent.length > prevLength) {
                  this.stateManager.updateState({ userMessageContentReady: false });
                }

                this.presentAssistantMessage();
                if (currentState.didRejectTool) {
                  this.loggerManager.agentInfoWithRequestId(reqUUID, 'didRejectTool:innerAbortController?.abort()');
                  innerAbortController?.abort();
                  isComplete = true;
                  assistantMessage += '\n\n[Response interrupted by user feedback]';
                  resolve();
                }
                if (currentState.didAlreadyUseTool) {
                  this.loggerManager.agentInfoWithRequestId(reqUUID, 'didAlreadyUseTool:innerAbortController?.abort()');
                  innerAbortController?.abort();
                  isComplete = true;
                  assistantMessage +=
                    '\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]';
                  resolve();
                }
                return;
              } catch (error: any) {
                this.loggerManager.logMessageParseError(reqUUID, error);
                this.messageService.say('error', `消息解析出错: ${error}`);
                reject(error);
              }
            },
            onerror: (e: any) => {
              this.loggerManager.logApiError(
                reqUUID,
                state.modelConfig.model,
                Date.now() - startApiTime,
                ErrorUtils.errorToJson(e)
              );
              if (!hasError && !isComplete) {
                // 处理错误...
                hasError = true;
                isComplete = true;
                reject(e);
              }
            }
          })
          .then(() => {
            this.loggerManager.updateTrace(output);
            this.loggerManager.endGeneration(output);
          });
      });
      try {
        // 使用Promise包装事件流处理
        await messageProcessingPromise;
        this.loggerManager.agentInfoWithRequestId(reqUUID, '消息处理已完成:' + assistantMessage);
      } catch (error) {
        // 捕获并记录错误，但仍然继续执行
        this.loggerManager.logMessageProcessError(reqUUID, error);
      } finally {
        // 后续处理
        this.stateManager.updateState({
          isStreaming: false,
          didCompleteReadingStream: true
        });

        const currentState = this.stateManager.getState();
        const partialBlocks = currentState.assistantMessageContent.filter((block) => block.partial);
        partialBlocks.forEach((block) => {
          block.partial = false;
          if (block.type === 'tool_use') {
            const toolUse = block as ToolUse;
            if (toolUse.paramsString) {
              try {
                toolUse.params = JSON.parse(toolUse.paramsString);
                toolUse.paramsString = '';
              } catch (error) {
                this.loggerManager.agentInfo(`Failed to parse tool use params: ${toolUse.paramsString}`);
                toolUse.params = {};
              }
            }
          }
        });
        if (partialBlocks.length > 0) {
          // 强制更新：通过重新设置相同的引用，确保状态管理器知道内容已经改变
          this.stateManager.updateState({ assistantMessageContent: currentState.assistantMessageContent });

          this.presentAssistantMessage();
        }
        let didEndLoop = false;
        if (currentState.assistantMessageContent.length > 0) {
          await this.messageService.addToApiConversationHistory({
            role: 'assistant',
            content: [
              {
                type: 'text',
                text: assistantMessage,
                category: 'assistant'
              }
            ],
            chatId: currentState.chatId
          });
          await pWaitFor(() => this.stateManager.getState().userMessageContentReady);
          const didToolUse = currentState.assistantMessageContent.some((block) => block.type === 'tool_use');

          if (!didToolUse) {
            await this.handleCompletedTask();
            return true;
          }
          this.loggerManager.logLLMSuccess(
            reqUUID,
            currentState.modelConfig.model,
            Date.now() - currentState.startLlmTime
          );

          const recDidEndLoop = await this.recursivelyMakeLLMRequests(currentState.userMessageContent);
          didEndLoop = recDidEndLoop || currentState.didCompleteTask;
        } else {
          if (!currentState.isTooLongForApiReq) {
            // await this.say('api_req_failed', 'API请求出错');
            this.loggerManager.logLLMError(
              reqUUID,
              currentState.modelConfig.model,
              Date.now() - currentState.startLlmTime,
              'api_req_failed'
            );
          }
          await this.messageService.addToApiConversationHistory({
            role: 'assistant',
            content: [
              {
                type: 'text',
                text: 'Failure: I did not provide a response.',
                category: 'assistant'
              }
            ],
            chatId: currentState.chatId
          });
          this.stateManager.updateState({
            consecutiveMistakeCount: currentState.consecutiveMistakeCount + 1
          });
        }

        return didEndLoop;
      }
    } catch (error: any) {
      const currentState = this.stateManager.getState();
      this.loggerManager.logLLMProcessError(
        currentState.modelConfig.model,
        Date.now() - currentState.startLlmTime,
        error
      );
      return true;
    }
  }

  private async handleApiRequestLimits(userContent: UserContent) {
    if (this.stateManager.getState().isTooLongForApiReq) {
      const { askResponse, text } = await this.messageService.ask(
        'mistake_limit_reached',
        this.stateManager.getState().tooLongTip
      );
      if (askResponse === 'messageResponse' && text) {
        userContent.push({ type: 'text', text, category: 'user-input' });
      }
      this.stateManager.updateState({ isTooLongForApiReq: false, tooLongTip: '' });
    }
    if (this.stateManager.getState().consecutiveMistakeCount >= 3) {
      const state = this.stateManager.getState();
      this.loggerManager.logMistakeLimitReached(state.modelConfig.model, Date.now() - state.startTaskTime);
      const { askResponse, text } = await this.messageService.ask(
        'mistake_limit_reached',
        i18n.__("agent.error")
      );
      if (askResponse === 'messageResponse') {
        userContent.push({ type: 'text', text: formatResponse.tooManyMistakes(text), category: 'format-response' });
      }
      this.stateManager.updateState({ consecutiveMistakeCount: 0 });
    }
  }

  private async presentAssistantMessage() {
    if (this.stateManager.getState().abort) {
      throw new Error('Kwaipilot instance aborted');
    }

    const state = this.stateManager.getState();

    // 如果函数被锁定，标记有待处理的更新，并返回
    if (state.presentAssistantMessageLocked) {
      this.stateManager.updateState({ presentAssistantMessageHasPendingUpdates: true });
      return;
    }

    // 执行中，锁定该函数，待处理为false
    this.stateManager.updateState({
      presentAssistantMessageLocked: true,
      presentAssistantMessageHasPendingUpdates: false
    });

    if (state.currentStreamingContentIndex >= state.assistantMessageContent.length) {
      if (state.didCompleteReadingStream) {
        this.stateManager.updateState({ userMessageContentReady: true });
      }
      this.stateManager.updateState({ presentAssistantMessageLocked: false });
      return;
    }

    const block = cloneDeep(state.assistantMessageContent[state.currentStreamingContentIndex]);
    switch (block.type) {
      case 'text': {
        if (state.didRejectTool || state.didAlreadyUseTool) {
          break;
        }
        let content = block.content;
        if (content) {
          content = content.replace(/<thinking>\s?/g, '');
          content = content.replace(/\s?<\/thinking>/g, '');
          const lastOpenBracketIndex = content.lastIndexOf('<');
          if (lastOpenBracketIndex !== -1) {
            const possibleTag = content.slice(lastOpenBracketIndex);
            const hasCloseBracket = possibleTag.includes('>');
            if (!hasCloseBracket) {
              // Extract the potential tag name
              let tagContent: string;
              if (possibleTag.startsWith('</')) {
                tagContent = possibleTag.slice(2).trim();
              } else {
                tagContent = possibleTag.slice(1).trim();
              }
              // Check if tagContent is likely an incomplete tag name (letters and underscores only)
              const isLikelyTagName = /^[a-zA-Z_]+$/.test(tagContent);
              // Preemptively remove < or </ to keep from these artifacts showing up in chat (also handles closing thinking tags)
              const isOpeningOrClosing = possibleTag === '<' || possibleTag === '</';
              // If the tag is incomplete and at the end, remove it from the content
              if (isOpeningOrClosing || isLikelyTagName) {
                content = content.slice(0, lastOpenBracketIndex).trim();
              }
            }
          }
        }
        if (!block.partial) {
          // Some models add code block artifacts (around the tool calls) which show up at the end of text content
          // matches ``` with at least one char after the last backtick, at the end of the string
          const match = content?.trimEnd().match(/```[a-zA-Z0-9_-]+$/);
          if (match) {
            const matchLength = match[0].length;
            content = content.trimEnd().slice(0, -matchLength);
          }
        }
        await this.messageService.say('text', content, block.partial);
        break;
      }
      case 'tool_use': {
        const currentState = this.stateManager.getState();
        const toolDescription = ToolHelpers.getToolDescription(block);
        if (currentState.didRejectTool) {
          if (!block.partial) {
            currentState.userMessageContent.push({
              type: 'text',
              text: `Skipping tool ${toolDescription} due to user rejecting a previous tool.`,
              category: 'tool-exception',
              toolName: block.name,
              params: block.params,
              toolId: block.id
            });
          } else {
            // partial tool after user rejected a previous tool
            currentState.userMessageContent.push({
              type: 'text',
              text: `Tool ${toolDescription} was interrupted and not executed due to user rejecting a previous tool.`,
              category: 'tool-exception',
              toolName: block.name,
              params: block.params,
              toolId: block.id
            });
          }
          this.stateManager.updateState({ userMessageContent: currentState.userMessageContent });
          break;
        }
        if (currentState.didAlreadyUseTool) {
          // ignore any content after a tool has already been used
          currentState.userMessageContent.push({
            type: 'text',
            text: `Tool [${block.name}] was not executed because a tool has already been used in this message. Only one tool may be used per message. You must assess the first tool's result before proceeding to use the next tool.`,
            category: 'tool-exception',
            toolName: block.name,
            params: block.params,
            toolId: block.id
          });
          this.stateManager.updateState({ userMessageContent: currentState.userMessageContent });
          break;
        }
        await this.toolExecutor.executeToolUse(block, currentState.userMessageContent);
        // 更新 state 中的 userMessageContent，因为工具处理器可能修改了它
        this.stateManager.updateState({ userMessageContent: currentState.userMessageContent });
        break;
      }
    }

    // 解锁消息处理
    this.stateManager.updateState({ presentAssistantMessageLocked: false });
    // 处理完成块或拒绝/已使用工具的情况
    const finalState = this.stateManager.getState();
    if (!block.partial || finalState.didRejectTool || finalState.didAlreadyUseTool) {
      // 检查是否是最后一个块
      if (finalState.currentStreamingContentIndex === finalState.assistantMessageContent.length - 1) {
        // its okay that we increment if !didCompleteReadingStream, it'll just return bc out of bounds and as streaming continues it will call presentAssistantMessage if a new block is ready. if streaming is finished then we set userMessageContentReady to true when out of bounds. This gracefully allows the stream to continue on and all potential content blocks be presented.
        // last block is complete and it is finished executing
        this.stateManager.updateState({ userMessageContentReady: true });
      }
      // 移动到下一个块
      // need to increment regardless, so when read stream calls this function again it will be streaming the next block
      this.stateManager.updateState({
        currentStreamingContentIndex: finalState.currentStreamingContentIndex + 1
      });
      // 如果还有更多块，递归处理
      if (finalState.currentStreamingContentIndex + 1 < finalState.assistantMessageContent.length) {
        // there are already more content blocks to stream, so we'll call this function ourselves
        this.presentAssistantMessage();
        return;
      }
    }
    // block is partial, but the read stream may have finished
    if (finalState.presentAssistantMessageHasPendingUpdates) {
      this.presentAssistantMessage();
    }
  }

  private async handleCompletedTask() {
    const stopReason = this.stateManager.getState().stopReason;
    await this.messageService.say('completion_result', '', false, undefined, stopReason);
    this.stateManager.updateState({ didCompleteTask: true });
    const state = this.stateManager.getState();
    this.loggerManager.logTaskCompleted(state.modelConfig.model, Date.now() - state.startTaskTime);
  }

  /** 用于上报 AI 代码生成，统计代码生成率 */
  async reportGenerateCode(request: ReportGenerateCodeRequest['editDataList']): Promise<void> {
    await this.httpClient.post(
      '/eapi/kwaipilot/log/edit-file',
      JSON.stringify({
        chatId: this.stateManager.getState().chatId,
        modelType: this.stateManager.getState().modelConfig.model,
        platform: this.sessionInfo?.reqData.deviceInfo?.platform,
        sessionId: this.stateManager.getState().sessionId,
        username: this.sessionInfo?.reqData.username || GlobalConfig.getConfig().getUsername(),
        editDataList: request
      })
    );
  }
}
