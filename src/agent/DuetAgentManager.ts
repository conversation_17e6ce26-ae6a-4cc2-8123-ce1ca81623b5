import { BaseAgentManager } from './AgentManager';
import { ToolSwitchState } from './utils/toolSwitches';
import { WebviewMessage } from '@/agent/types/type';
import { IMessenger } from '@/protocol/messenger';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';

export class DuetAgentManager extends BaseAgentManager {
  constructor(
    messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    cwd: string,
    options: {
      model: string;
      maxTokens: number;
      isEnableRepoIndex: boolean;
      isUseNewTool: boolean;
      toolSwitchState?: ToolSwitchState;
      isSupportToolUse: boolean;
      isAskMode?: boolean;
    },
    sessionInfo?: WebviewMessage<'newTask'>
  ) {
    super(messenger, cwd, options, sessionInfo);
  }

  // 异步 constructor，保证前期配置一定加载完成
  static async init(
    messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    cwd: string,
    sessionInfo?: WebviewMessage<'newTask'>,
    toolSwitchState?: ToolSwitchState,
    isAskMode?: boolean,
  ) {
    // 获取基础配置
    const config = await this.getCommonConfig(sessionInfo);

    return new DuetAgentManager(
      messenger,
      cwd,
      {
        ...config,
        toolSwitchState,
        isAskMode
      },
      sessionInfo
    );
  }

}
