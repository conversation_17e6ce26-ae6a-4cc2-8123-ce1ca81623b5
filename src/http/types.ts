export interface HttpClientResponse<T> {
  data: T;
  host: string;
  port: number;
  message: string;
  status: number;
  timestamp: string;
  traceId: string;
}

export interface QueryEmbeddingBody {
  chat_history: {
    content: string;
    role: string;
  }[];
  enable_rewrite: boolean;
  query: string;
}
export interface QueryEmbeddingResponse {
  code_embedding: number[];
  nl_embedding: number[];
  rerank_query: string;
  sparse_embedding: Record<string, number>;
}

export interface CodeRerankBody {
  code_context_list: {
    is_sub_node: boolean;
    code_content: string;
    metadata: {
      callees: string[];
      callers: string[];
      code_type: string;
      file_path: string;
      language: string;
      name: string;
      signature: string;
    };
    node_id: string;
    sub_node_id: number;
  }[];
  query: string;
  top_k: number;
}
export interface CodeRerankResponse {
  code_context_list: {
    _sub_node: boolean;
    code_content: string;
    metadata: {
      callees: string[];
      callers: string[];
      code_type: string;
      file_path: string;
      language: string;
      name: string;
      signature: string;
    };
    node_id: string;
    sub_node_id: number;
  }[];
}

export interface GeneratePromptFile {
  code: string;
  language: string;
  name: string;
}
export interface CloudCodeSearchBody {
  files: GeneratePromptFile[]; // 可选，不传就行
  searchTargetDirs: string[];
  query: string;
  codebaseSearch: boolean; // 一定要 true
  commit: string;
  repoName: string;
  username: string;
  instantApplyMode: boolean; // false 即可
  topK: number;
}

export interface CloudCodeSearchResponse {
  prompt: string;
  list: {
    id: number;
    path: string;
    startLineNo: number;
    endLineNo: number;
    startColNo: number;
    endColNo: number;
    code: string;
    language: string;
    repoName: string;
  }[];
}

export interface SearchParams {
  query: string;
  topK: number;
  targetDirectory: string[];
  chatHistory: any;
  enable_rewrite?: boolean;
  // 云端索引相关参数
  gitRepo?: string;
  commit?: string;
  username?: string;
  enableCloudSearch?: boolean;
  // 远程索引增加的参数
  dirPath: string;
}

export interface RetrieveDataItem {
  node_id: string;
  sub_node_id: number;
  is_sub_node: boolean;
  code_content: string;
  metadata: {
    name: string;
    signature: string;
    language: string;
    file_path: string;
    code_type: string;
    callers: string[];
    callees: string[];
  };
}

export interface IndexingSearchBody {
  targetDirectory: string[];
  query: string;
  user_id: string;
  repo_id: string;
  commit_id: string;
  enable_rewrite: boolean;
  topK: number;
  chatHistory: {
    content: string;
    role: string;
  }[];
}

export interface IndexingSearchResult {
  content: string;
  repo_id: string;
  file_name: string;
  file_path: string;
  file_lang: string;
  code_name: string;
  code_type: string;
  metadata: {
    line_start: number;
    line_end: number;
    column_start: number;
    column_end: number;
  };
  hash: string;
  _score: number;
  _id: string;
}

export interface IndexingSearchCodeContext {
  node_id: string;
  sub_node_id: number;
  code_content: string;
  metadata: {
    name: string;
    signature: string;
    language: string;
    file_path: string;
    code_type: string;
    callers: string[];
    callees: string[];
    line_start: number;
    line_end: number;
    column_start: number;
    column_end: number;
  };
  _sub_node: boolean;
  enhanced_metadata: {
    line_start: number;
    line_end: number;
    column_start: number;
    column_end: number;
  };
}

export interface IndexingSearchResponse {
  user_results: IndexingSearchResult[];
  base_results: IndexingSearchResult[];
  rerank_result: {
    code_context_list: IndexingSearchCodeContext[];
  };
}
/**
 * Interface for the result of processing a single file.
 */
export interface ProcessFileResult {
  filepath: string;
  action: string;
  success: boolean;
  error?: string;
  fileId?: number;
}
/**
 * Interface for the response payload when indexing files.
 */
export interface IndexUserFilesResponse {
  repoId: string;
  username: string;
  deviceId: string;
  dirPath: string;
  repoUrl?: string;
  succeedFiles: ProcessFileResult[];
  failedFiles: ProcessFileResult[];
}
