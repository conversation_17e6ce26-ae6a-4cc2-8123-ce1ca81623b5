import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/protocol/messenger';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { ResponseBase } from '@/protocol/index.d';
import { Logger } from '@/util/log';

export class IdeStateManager {
  private checkCount: number = 0;
  private readonly MAX_RETRY = 3;
  private readonly CHECK_INTERVAL = 60000; // 1分钟检查间隔
  private readonly CHECK_TIMEOUT = 5000; // 5秒检查超时
  private checkTimer?: NodeJS.Timeout;
  private logger: Logger;

  /**
   * Check if a process with the given pid is still running and is not the root process
   */
  private isProcessRunning(pid: number): boolean {
    try {
      // Don't consider PID 1 (root/init process) as valid parent
      if (pid <= 1) {
        // 如果父进程是主进程，那么直接退出
        process.exit(1);
        return false;
      }
      // Signal 0 doesn't send a signal but checks if process exists
      process.kill(pid, 0);
      return true;
    } catch (e) {
      return false;
    }
  }

  constructor(private messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>) {
    this.logger = new Logger('IdeStateManager');

    this.startChecking();
  }

  public startChecking() {
    this.logger.info('Starting parent process check');
    this.logger.reportUserAction({
      key: 'ide_state_check',
      content: 'Parent process check started',
      type: 'ide_state_check_start',
      subType: this.checkCount.toString()
    });
    this.checkTimer = setInterval(() => this.checkParentProcess(), this.CHECK_INTERVAL);
  }

  public stopChecking() {
    if (this.checkTimer) {
      this.logger.info(`Stopping parent process check, total checks: ${this.checkCount}`);
      this.logger.reportUserAction({
        key: 'ide_state_check',
        content: 'Parent process check stopped',
        type: 'ide_state_check_stop',
        subType: this.checkCount.toString()
      });
      clearInterval(this.checkTimer);
      this.checkTimer = undefined;
    }
    this.checkCount = 0;
  }

  private async checkParentProcess() {
    this.logger.reportUserAction({
      key: 'ide_state_check',
      content: `Checking parent process, attempt ${this.checkCount + 1}/${this.MAX_RETRY}`,
      type: 'ide_state_check_attempt',
      subType: this.checkCount.toString()
    });

    try {
      // Set a timeout for the parent process check
      const checkPromise = new Promise<boolean>((resolve) => {
        const ppid = process.ppid;
        resolve(ppid ? this.isProcessRunning(ppid) : false);
      });

      // Race the check against a timeout
      const result = await Promise.race([
        checkPromise,
        new Promise<boolean>((resolve) => setTimeout(() => resolve(false), this.CHECK_TIMEOUT))
      ]);

      if (result) {
        this.logger.info(`Checking parent process success, ppid: ${process.ppid}`);
        this.checkCount = 0;
        this.logger.info('Parent process check successful');
        this.logger.reportUserAction({
          key: 'ide_state_check',
          content: 'Parent process exists',
          type: 'ide_state_check_success',
          subType: this.checkCount.toString()
        });
        return;
      }

      this.logger.info(`Checking parent process, attempt ${this.checkCount + 1}/${this.MAX_RETRY}`);
      this.handleCheckFailure('Parent process not detected or check timed out');
    } catch (error) {
      this.handleCheckFailure(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  private handleCheckFailure(reason: string) {
    this.checkCount++;
    this.logger.warn(`Parent process check failed (${this.checkCount}/${this.MAX_RETRY}): ${reason}`);
    this.logger.reportUserAction({
      key: 'ide_state_check',
      content: reason,
      type: 'ide_state_check_failed',
      subType: this.checkCount.toString()
    });

    if (this.checkCount >= this.MAX_RETRY) {
      this.logger.error(`Parent process check failed ${this.MAX_RETRY} times, exiting process. Last error: ${reason}`);
      this.logger.reportUserAction({
        key: 'ide_state_check',
        content: reason,
        type: 'ide_state_check_failed_exit',
        subType: this.checkCount.toString()
      });
      this.logger.perf({
        namespace: 'ide_state_check',
        subtag: 'ide_state_check_failed_exit',
        millis: this.checkCount,
        extra4: reason
      });
      this.stopChecking();
      process.exit(1);
    }
  }
}
