process.env.IS_BINARY = 'true';

import { Command } from 'commander';
import { Core } from './core';
import { FromCoreProtocol, ToCoreProtocol } from './protocol';
import { IMessenger } from './protocol/messenger';
import { getCoreLogsPath } from '@/util/paths';
import fs from 'node:fs';
import { setupCoreLogging } from '@/entry/logging';
import { TcpMessenger } from '@/entry/TcpMessenger';
import { Logger } from '@/util/log';
import { AGENT_NAMESPACE } from '@/util/const';
import { GlobalConfig } from '@/util/global';
import i18n from './i18n';
const logFilePath = getCoreLogsPath();

fs.appendFileSync(logFilePath, '[info] Starting Kwaipilot agent...\n');

const program = new Command();
const logger = new Logger('main');

program.action(async () => {
  try {
    setupCoreLogging();
    logger.info('Starting Kwaipilot agent...' + JSON.stringify(process.env));
    const messenger: IMessenger<ToCoreProtocol, FromCoreProtocol> = new TcpMessenger<
      ToCoreProtocol,
      FromCoreProtocol
    >();

    const core = new Core(messenger);
    await core.init();
    logger.info('kwaipilot binary started successfully');
  } catch (e: any) {
    fs.writeFileSync('./error.log', `${new Date().toISOString()} ${e}\n`);
    logger.error(`Error: ${e}`);
    logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'unknownError',
      millis: 1,
      extra3: GlobalConfig.getConfig().getUsername(),
      extra5: typeof e === 'object' ? e.message : String(e),
      extra6: new Error().stack || ''
    });
    process.exit(1);
  }
});

program.parse(process.argv);
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception: ', error);
  logger.reportUserAction({
    key: 'uncaughtException',
    content: typeof error === 'object' ? error.message : String(error),
    subType: GlobalConfig.getConfig().getUsername(),
    type: GlobalConfig.getConfig().getRepoPath()
  });
  logger.perf({
    namespace: AGENT_NAMESPACE,
    subtag: 'uncaughtException',
    millis: 1,
    extra3: GlobalConfig.getConfig().getUsername(),
    extra5: typeof error === 'object' ? error.message : String(error),
    extra6: new Error().stack || ''
  });
});