import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { McpConnection, McpTool } from './types';
import { ServerConfigSchema } from './settingSchema';
import { ListToolsResultSchema, LoggingMessageNotificationSchema, ProgressNotificationSchema } from '@modelcontextprotocol/sdk/types.js';
import { formatValidationError } from './parserHelper/validationHelper';
import { MCPLogger } from '@/util/log';
import { getUserNodePath } from './utils';

/** MCP连接请求的默认超时时间（毫秒） */
const DEFAULT_REQUEST_TIMEOUT_MS = 5000;

/** 重试配置默认值 */
const DEFAULT_RETRY_CONFIG = {
  maxRetries: 10,
  initialDelay: 1000, // 1秒
  maxDelay: 30000, // 30秒
  backoffMultiplier: 2,
  retryableErrors: [
    'SSE error: TypeError: terminated:',
    'ECONNREFUSED',
    'ENOTFOUND',
    'ETIMEDOUT',
    'ECONNRESET'
  ]
};

/**
 * 重试配置接口
 */
interface RetryConfig {
  maxRetries: number;
  initialDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableErrors: string[];
}

/**
 * 连接重试状态
 */
interface RetryState {
  isRetrying: boolean;
  retryCount: number;
  lastRetryTime: number;
  nextRetryDelay: number;
}

/**
 * MCP连接管理器
 * 负责管理所有MCP服务器的连接生命周期，包括：
 * - 创建和维护连接
 * - 建立和断开连接
 * 管理连接状态
 * - 处理连接错误
 * - 自动重试连接
 */
export class McpConnectionManager {
  /** 当前活动的MCP连接列表 */
  private connections: McpConnection[] = [];

  /** MCP客户端版本号 */
  private clientVersion: string;

  /** 服务器状态变更的回调函数 */
  private onServerChange: () => void;

  /** 重试配置 */
  private retryConfig: RetryConfig;

  /** 连接重试状态映射 */
  private retryStates: Map<string, RetryState> = new Map();

  /** 重试定时器映射 */
  private retryTimers: Map<string, NodeJS.Timeout> = new Map();

  private logger;

  /**
   * 创建MCP连接管理器实例
   * @param clientVersion - MCP客户端版本号
   * @param onServerChange - 服务器状态变更时的回调函数
   * @param retryConfig - 可选的重试配置
   */
  constructor(clientVersion: string, onServerChange: () => void, retryConfig?: Partial<RetryConfig>) {
    this.clientVersion = clientVersion;
    this.onServerChange = onServerChange;
    this.retryConfig = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };
    this.logger = new MCPLogger(`mcp-connection-${this.clientVersion}`);
  }

  /**
   * 获取所有活动的MCP连接
   * @returns 当前所有的MCP连接列表
   */
  public getConnections(): McpConnection[] {
    return this.connections;
  }

  /**
   * 获取指定名称的MCP连接
   * @param name - MCP服务器名称
   * @returns 找到的连接对象，如果不存在则返回undefined
   */
  public getConnection(name: string): McpConnection | undefined {
    return this.connections.find((conn) => conn.server.name === name);
  }

  /**
   * 创建新的MCP连接对象
   * 根据配置创建客户端实例和传输层，但不建立实际连接
   *
   * @param name - MCP服务器名称
   * @param config - 服务器配置信息
   * @returns 新创建的连接对象
   */
  public async createConnection(name: string, jsonConfig: unknown): Promise<McpConnection> {
    this.logger.info('createConnection start', name, jsonConfig);

    // 如果解析失败的，可以字节返回disconnected
    const result = ServerConfigSchema.safeParse(jsonConfig);

    if (!result.success) {
      const connection: McpConnection = {
        server: {
          name,
          config: JSON.stringify(result),
          status: 'disconnected',
          error: formatValidationError(result.error, 'MCP设置文件').message
        }
      };

      this.logger.info(`ServerConfigSchema 解析失败: ${name}`, connection);

      this.connections.push(connection);

      return connection;
    }

    const config = result.data;

    // 创建客户端实例
    const client = new Client(
      {
        name: 'Kwaipilot-client',
        version: this.clientVersion
      },
      {
        capabilities: {}
      }
    );

    // 根据配置类型创建对应的传输层
    let transport: StdioClientTransport | SSEClientTransport;
    if (config.transportType === 'sse') {
      const sseOptions: any = {};

      // 如果配置了自定义headers，则添加到eventSourceInit中
      if (config.headers && Object.keys(config.headers).length > 0) {
        sseOptions.eventSourceInit = {
          fetch: async (url: string, init: RequestInit) => {
            this.logger.info('createConnection sse headers:', config.headers);
            return fetch(url, {
              ...init,
              headers: {
                ...init.headers,
                ...config.headers,
                'Accept': 'text/event-stream'
              }
            });
          }
        };
        sseOptions.requestInit = {
          headers: {
            ...config.headers,
          }
        };
      }

      transport = new SSEClientTransport(new URL(config.url), sseOptions);
    } else {
      let command = ((config && config.command) || '').trim();

      // node 命令需要使用用户设置的 node，这里需要特殊处理
      if (command === 'node') {
        this.logger.info('createConnection isNodeCommand');

        command = await getUserNodePath();

        this.logger.info('createConnection getNodePath:', command);
      }

      transport = new StdioClientTransport({
        command,
        args: config.args,
        env: {
          ...(process.env.PATH ? { PATH: process.env.PATH } : {}),
          ...(config.env || {})
        },
        stderr: 'pipe'
      });
    }

    // transport 配置错误处理
    // stdio 下走该逻辑
    transport.onerror = async (error) => {
      this.logger.error(`mcp server ${name} 连接错误`, error);
      const connection = this.getConnection(name);
      if (connection) {
        connection.server.status = 'disconnected';
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.appendErrorMessage(connection, errorMessage);
        this.appendFullErrorMessage(connection, errorMessage);
      }
      this.onServerChange();
    };

    // transport 配置关闭处理
    // stdio 下走该逻辑
    transport.onclose = async () => {
      this.logger.info(`mcp server ${name} 连接关闭`);
      const connection = this.getConnection(name);
      if (connection) {
        connection.server.status = 'disconnected';
      }
      this.onServerChange();
    };

    if (config.transportType === 'sse') {
      // client 配置错误处理
      client.onerror = async (error) => {
        this.logger.error(`mcp server ${name} client错误`, error);
        const connection = this.getConnection(name);

        if (connection) {
          const errorMessage = error instanceof Error ? error.message : String(error);

          if (this.isRetryableError(errorMessage)) {
            connection.server.status = 'disconnected';
          }

          this.appendErrorMessage(connection, errorMessage);
          this.appendFullErrorMessage(connection, errorMessage);

          // 处理连接错误，决定是否重试
          this.handleConnectionError(name, errorMessage);
        }
        this.onServerChange();
      };

      // client 配置关闭处理
      client.onclose = async () => {
        this.logger.info(`mcp server ${name} client关闭`);
        const connection = this.getConnection(name);
        if (connection) {
          connection.server.status = 'disconnected';
        }
        this.onServerChange();
      };
    }

    client.setNotificationHandler(LoggingMessageNotificationSchema, (notification) => {
      this.logger.info(`mcp server ${name} 通知`, notification);
    });

    client.setNotificationHandler(ProgressNotificationSchema, (notification) => {
      this.logger.info(`mcp server ${name} 进度`, notification);
    });

    // 创建并初始化连接对象
    const connection: McpConnection = {
      server: {
        name,
        config: JSON.stringify(config),
        status: config.disabled ? 'disconnected' : 'connecting',
        disabled: config.disabled,
      },
      client,
      transport,
      transportType: config.transportType,
    };

    this.logger.info(`createConnection success: ${name}`, connection);

    // 添加到连接列表
    this.connections.push(connection);

    return connection;
  }

  /**
   * 建立MCP服务器连接
   * 处理连接初始化、错误处理和工具列表获取
   *
   * @param connection - 要建立连接的MCP连接对象
   * @throws Error 当连接建立失败时抛出错误
   */
  public async establishConnection(connection: McpConnection, retry = 0): Promise<void> {
    const { name, config, disabled } = connection.server;

    if (!connection.transport || !connection.client) {
      connection.server.status = 'disconnected';
      this.onServerChange();
      return;
    }

    this.logger.info('establishConnection start', connection);

    try {
      const configObj = JSON.parse(config);

      // 处理stdio类型连接的特殊配置
      if (configObj.transportType === 'stdio') {
        await connection.transport.start();
        const stderrStream = (connection.transport as StdioClientTransport).stderr;
        if (stderrStream) {
          stderrStream.on('data', async (data: Buffer) => {
            const output = data.toString();
            const isInfoLog = !/\berror\b/i.test(output);

            if (isInfoLog) {
              this.logger.info(`Server "${name}" info:`, output);
            } else {
              this.logger.error(`Server "${name}" error:`, output);
              const conn = this.getConnection(name);
              if (conn) {
                this.appendErrorMessage(conn, output);
                this.appendFullErrorMessage(conn, output);
                this.onServerChange();
              }
            }
          });
        } else {
          this.logger.error(`No stderr stream for ${name}`);
        }

        connection.transport.start = async () => {
          // no-op
        };
      }

      // 建立实际连接
      await connection.client.connect(connection.transport);

      // 更新连接状态
      connection.server.status = 'connected';
      connection.server.error = '';
      connection.server.fullError = '';

      // 标记连接曾经成功过
      connection.server.prevConnected = true;

      // 连接成功，清除重试状态
      this.clearRetryState(name);

      this.onServerChange();

      // 获取并保存工具列表
      connection.server.tools = await this.fetchToolsList(connection);
      this.logger.debug(`获取MCP Server tools ${name}`, connection.server.tools);
    } catch (error) {
      this.logger.error(`建立连接失败: ${name}:`, error);
      connection.server.status = 'disconnected';
      this.onServerChange();
      throw error;
    }
  }

  /**
   * 删除指定的MCP连接
   * 关闭连接并清理相关资源
   *
   * @param name - 要删除的连接名称
   */
  public async removeConnection(name: string): Promise<void> {
    const connection = this.getConnection(name);
    if (connection) {
      this.connections = this.connections.filter((conn) => conn.server.name !== name);
      connection.server.status = 'disconnected';

      // 清理重试状态
      this.clearRetryState(name);

      this.onServerChange();

      try {
        await connection.transport?.close();
        await connection.client?.close();
      } catch (error) {
        this.logger.error(`删除连接失败: ${name}:`, error);
      }
    }
  }

  /**
   * 切换连接的启用/禁用状态
   * 当禁用时会关闭连接，启用时会自动建立连接
   *
   * @param name - 连接名称
   * @param disabled - 是否禁用
   * @throws Error 当连接不存在或状态切换失败时
   */
  public async toggleConnectionState(name: string, disabled: boolean): Promise<void> {
    const connection = this.getConnection(name);
    if (!connection) {
      throw new Error(`Connection ${name} not found`);
    }

    connection.server.disabled = disabled;

    if (disabled) {
      // 禁用时关闭现有连接并清理重试状态
      await this.removeConnection(name);
      // 重新创建未连接的实例以保持配置
      const config = JSON.parse(connection.server.config);
      config.disabled = disabled;
      await this.createConnection(name, config);
    } else {
      // 启用时自动建立连接
      try {
        await this.removeConnection(name);
        const config = JSON.parse(connection.server.config);
        config.disabled = disabled;
        const newConnection = await this.createConnection(name, config);
        await this.establishConnection(newConnection);
        this.onServerChange();
      } catch (error) {
        this.logger.error(`切换连接失败: ${name}:`, error);
        this.onServerChange();
        throw error;
      }
    }
  }

  /**
   * 获取MCP服务器支持的工具列表
   *
   * @param connection - MCP连接对象
   * @returns 工具列表，如果获取失败则返回空数组
   */
  private async fetchToolsList(connection: McpConnection): Promise<McpTool[]> {
    try {
      const response = await connection.client?.request(
        {
          method: 'tools/list'
        },
        ListToolsResultSchema,
        {
          timeout: DEFAULT_REQUEST_TIMEOUT_MS
        }
      );

      const tools = (response?.tools || []).map((tool) => ({
        ...tool,
        autoApprove: false
      }));

      return tools;
    } catch (error) {
      this.logger.error(`获取工具列表失败: ${connection.server.name}:`, error);
      return [];
    }
  }

  /**
   * 添加错误信息到连接对象
   *
   * @param connection - MCP连接对象
   * @param error - 错误信息
   */
  private appendErrorMessage(connection: McpConnection, error: string) {
    // 不去聚合多次的错误信息，只返回最后一条信息
    // const newError = connection.server.error ? `${connection.server.error}\n${error}` : error;
    const newError = error;
    connection.server.error = newError;
  }

  private appendFullErrorMessage(connection: McpConnection, error: string) {
    const errorWithTime = `[${this.getNowTimeStr()}] ${error}`;
    const prev = connection.server.fullError ? connection.server.fullError.split('\n') : [];
    // 新错误插入最前面
    prev.unshift(errorWithTime);
    // 只保留前 50 条（最新的在最上面）
    const first50 = prev.slice(0, 50);
    connection.server.fullError = first50.join('\n');
  }

  private getNowTimeStr() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 判断错误是否可重试
   * @param error - 错误信息
   * @returns 是否可重试
   */
  private isRetryableError(error: string): boolean {
    if (!error) return false;
    return this.retryConfig.retryableErrors.some(retryableError =>
      error.includes(retryableError)
    );
  }

  /**
   * 计算下次重试延迟时间（指数退避）
   * @param retryCount - 当前重试次数
   * @returns 延迟时间（毫秒）
   */
  private calculateRetryDelay(retryCount: number): number {
    const delay = this.retryConfig.initialDelay * Math.pow(this.retryConfig.backoffMultiplier, retryCount);
    return Math.min(delay, this.retryConfig.maxDelay);
  }

  /**
   * 初始化连接的重试状态
   * @param name - 连接名称
   */
  private initializeRetryState(name: string): void {
    this.retryStates.set(name, {
      isRetrying: false,
      retryCount: 0,
      lastRetryTime: 0,
      nextRetryDelay: this.retryConfig.initialDelay
    });
  }

  /**
   * 获取连接的重试状态
   * @param name - 连接名称
   * @returns 重试状态
   */
  private getRetryState(name: string): RetryState | undefined {
    return this.retryStates.get(name);
  }

  /**
   * 更新重试状态
   * @param name - 连接名称
   * @param updates - 要更新的状态
   */
  private updateRetryState(name: string, updates: Partial<RetryState>): void {
    const currentState = this.retryStates.get(name);
    if (currentState) {
      this.retryStates.set(name, { ...currentState, ...updates });
    }
  }

  /**
   * 清除重试状态
   * @param name - 连接名称
   */
  private clearRetryState(name: string): void {
    this.retryStates.delete(name);
    this.cancelRetry(name);
  }

  /**
   * 取消重试
   * @param name - 连接名称
   */
  private cancelRetry(name: string): void {
    const timer = this.retryTimers.get(name);
    if (timer) {
      clearTimeout(timer);
      this.retryTimers.delete(name);
    }
  }

  /**
   * 执行重试连接
   * @param name - 连接名称
   * @param error - 导致重试的错误
   */
  private async executeRetry(name: string, error: string): Promise<void> {
    const connection = this.getConnection(name);
    if (!connection || connection.server.disabled) {
      this.clearRetryState(name);
      return;
    }

    const retryState = this.getRetryState(name);
    if (!retryState) {
      this.initializeRetryState(name);
    }

    const currentState = this.getRetryState(name)!;

    // 检查是否超过最大重试次数
    if (currentState.retryCount >= this.retryConfig.maxRetries) {
      this.logger.warn(`连接 ${name} 已达到最大重试次数 (${this.retryConfig.maxRetries})，停止重试`);
      this.clearRetryState(name);
      return;
    }

    // 检查是否已经在重试中
    if (currentState.isRetrying) {
      this.logger.debug(`连接 ${name} 已在重试中，跳过`);
      return;
    }

    // 更新重试状态
    const nextRetryCount = currentState.retryCount + 1;
    const nextRetryDelay = this.calculateRetryDelay(nextRetryCount);

    this.updateRetryState(name, {
      isRetrying: true,
      retryCount: nextRetryCount,
      lastRetryTime: Date.now(),
      nextRetryDelay
    });

    this.logger.info(`连接 ${name} 将在 ${nextRetryDelay}ms 后进行第 ${nextRetryCount} 次重试`);

    // 设置重试定时器
    const timer = setTimeout(async () => {
      try {
        this.logger.info(`开始重试连接: ${name} (第 ${nextRetryCount} 次)`);

        // 重新创建连接
        const config = JSON.parse(connection.server.config);
        await this.removeConnection(name);
        const newConnection = await this.createConnection(name, config);

        // 尝试建立连接
        await this.establishConnection(newConnection);

        // 连接成功，清除重试状态
        this.logger.info(`连接 ${name} 重试成功`);
        this.clearRetryState(name);

      } catch (retryError) {
        this.logger.error(`连接 ${name} 重试失败 (第 ${nextRetryCount} 次):`, retryError);

        // 更新重试状态，准备下次重试
        this.updateRetryState(name, { isRetrying: false });

        // 检查是否继续重试
        if (nextRetryCount < this.retryConfig.maxRetries) {
          // 递归调用，继续重试
          await this.executeRetry(name, retryError instanceof Error ? retryError.message : String(retryError));
        } else {
          this.logger.warn(`连接 ${name} 重试次数已达上限，停止重试`);
          this.clearRetryState(name);
        }
      }
    }, nextRetryDelay);

    this.retryTimers.set(name, timer);
  }

  /**
   * 处理连接错误并决定是否重试
   * @param name - 连接名称
   * @param error - 错误信息
   */
  private handleConnectionError(name: string, error: string): void {
    const connection = this.getConnection(name);
    if (!connection || connection.server.disabled) {
      return;
    }

    // 只有曾经成功连接过的服务器才进行重试
    if (!connection.server.prevConnected) {
      this.logger.warn(`连接 ${name} 从未成功连接过，不进行重试`);
      this.clearRetryState(name);
      return;
    }

    // 检查错误是否可重试
    if (this.isRetryableError(error)) {
      this.logger.info(`连接 ${name} 发生可重试错误: ${error}`);
      this.executeRetry(name, error);
    } else {
      this.logger.warn(`连接 ${name} 发生不可重试错误: ${error}`);
      this.clearRetryState(name);
    }
  }

  /**
   * 手动重试连接
   * @param name - 连接名称
   * @returns 重试操作是否成功启动
   */
  public async manualRetry(name: string): Promise<boolean> {
    const connection = this.getConnection(name);
    if (!connection) {
      this.logger.error(`连接 ${name} 不存在`);
      return false;
    }

    if (connection.server.disabled) {
      this.logger.warn(`连接 ${name} 已禁用，无法重试`);
      return false;
    }

    if (connection.server.status === 'connected') {
      this.logger.info(`连接 ${name} 已连接，无需重试`);
      return true;
    }

    // 检查是否曾经成功连接过
    if (!connection.server.prevConnected) {
      this.logger.warn(`连接 ${name} 从未成功连接过，无法手动重试`);
      return false;
    }

    // 清除之前的重试状态
    this.clearRetryState(name);

    // 立即执行重试
    await this.executeRetry(name, 'Manual retry triggered');
    return true;
  }

  /**
   * 获取连接的重试信息
   * @param name - 连接名称
   * @returns 重试信息
   */
  public getRetryInfo(name: string): RetryState | null {
    return this.retryStates.get(name) || null;
  }

  /**
   * 更新重试配置
   * @param newConfig - 新的重试配置
   */
  public updateRetryConfig(newConfig: Partial<RetryConfig>): void {
    this.retryConfig = { ...this.retryConfig, ...newConfig };
    this.logger.info('重试配置已更新:', this.retryConfig);
  }

  /**
   * 清理所有重试状态和定时器
   */
  public cleanup(): void {
    // 清除所有重试定时器
    this.retryTimers.forEach((timer, name) => {
      clearTimeout(timer);
    });
    this.retryTimers.clear();

    // 清除所有重试状态
    this.retryStates.clear();

    this.logger.info('已清理所有重试状态和定时器');
  }
}
