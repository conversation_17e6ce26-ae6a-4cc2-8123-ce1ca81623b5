import { Client } from '@modelcontextprotocol/sdk/client/index.js';

import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { ResponseBase } from '@/protocol/index.d';
import { IMessenger } from '@/protocol/messenger';
import { FromCoreProtocol, ToCoreProtocol } from '@/protocol';

/**
 * MCP客户端接口定义
 * 定义了MCP客户端需要实现的核心功能
 */
export interface IMcpClient {
  /**
   * 释放客户端资源
   * 清理所有连接、监听器和其他资源
   */
  dispose(): Promise<void>;

  /**
   * 设置MCP客户端的messenger
   * @param messenger - 要设置的messenger实例
   */
  setMessenger(messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>): void;

  /**
   * 获取所有已打开且已连接的 MCP Server 列表
   * @returns McpServer[] MCP Server列表
   */
  getOpenedAndConnectedServers(): McpServer[];

  /**
   * 调用指定 MCP Server 上的工具
   * @param serverName - 目标 MCP Server 名称
   * @param toolName - 要调用的工具名称
   * @param toolArguments - 工具调用参数
   * @returns Promise<McpToolCallResponse> 工具执行结果
   */
  callTool(serverName: string, toolName: string, toolArguments?: Record<string, unknown>): Promise<McpToolCallResponse>;

  /**
   * 获取 MCP Server 列表，可选择按状态筛选
   * @param filterStatus - 可选的 MCP Server 状态过滤条件
   * @returns Promise<McpServer[]> 过滤后的 MCP Server 列表
   */
  getDisplayServers(
    filterStatus?: McpServer['status']
  ): Promise<ResponseBase<{ mcpServers: McpServer[]; isError: boolean }>>;

  /**
   * 获取MCP设置文件路径
   * @returns 设置文件路径
   */
  getSettingsPath(): Promise<ResponseBase<string>>;

  /**
   * 切换MCP Server 状态
   * @param params -  MCP Server 名称和状态
   * @returns 操作结果
   */
  toggleMcpServer(params: { serverName: string; disabled: boolean }): Promise<ResponseBase<boolean>>;

  /**
   * 重启MCP Server 
   * @param params -  MCP Server 名称
   * @returns 操作结果
   */
  restartMcpServer(params: { serverName: string }): Promise<ResponseBase<boolean>>;

  /**
   * 删除MCP Server 
   * @param params -  MCP Server 名称
   * @returns 操作结果
   */
  deleteMcpServer(params: { serverName: string }): Promise<ResponseBase<boolean>>;

  /**
   * 安装指定的MCP（模块化组件包）。
   * @param params 安装MCP所需的参数。
   * @returns 返回一个Promise，解析为ResponseBase<boolean>，表示安装是否成功。
   */
  installMcp(params: InstallMcpParams): Promise<ResponseBase<boolean>>;

  /**
   * 根据市场条件分页获取可用的MCP列表。
   * @param params 查询参数，包括页码、每页数量和搜索关键字。
   * @returns 返回一个Promise，解析为ResponseBase<{ page, pageSize, total, records }>，其中records为MarketMcpDetail数组。
   */
  fetchAvailableMcpListByMarket(params: {
    page?: number;
    pageSize?: number;
    searchKeyword?: string;
  }): Promise<ResponseBase<{
    page?: number;
    pageSize?: number;
    total?: number;
    records?: MarketMcpDetail[];
  }>>;

  /**
   * 根据市场serverId获取指定MCP的详细信息。
   * @param params 包含serverId的对象。
   * @returns 返回一个Promise，解析为ResponseBase<MarketMcpDetail>，包含MCP的详细信息。
   */
  fetchMcpDetailByMarket(params: { serverId: string; }): Promise<ResponseBase<MarketMcpDetail>>;

  /**
   * 检查并重启所有需要自动重启的MCP服务器
   */
  checkMcpServers(): void;
}

/**
 * MCP (Model Context Protocol)  MCP Server 配置类型定义
 * 用于描述 MCP Server 的配置信息、状态和可用工具等
 */
export type McpServer = {
  /**  MCP Server 唯一标识名称 */
  name: string;
  /**  MCP Server 的JSON格式配置信息 */
  config: string;
  /**
   *  MCP Server 当前连接状态
   * - connected: 已连接
   * - connecting: 连接中
   * - disconnected: 已断开连接
   */
  status: 'connected' | 'connecting' | 'disconnected';
  /**  MCP Server 发生错误时的错误信息 */
  error?: string;
  /** 该 MCP Server 提供的可用工具列表 */
  tools?: McpTool[];
  /**  MCP Server 是否被禁用 */
  disabled?: boolean;
  /**  MCP Server 操作超时时间（毫秒） */
  timeout?: number;
  /**  MCP Server 的全部错误信息 */
  fullError?: string;
  /**  MCP Server 上一次成功过 */
  prevConnected?: boolean;
};

export type McpConnection = {
  server: McpServer;
  client?: Client;
  transport?: StdioClientTransport | SSEClientTransport;
  transportType?: 'stdio' | 'sse';
};

/**
 * MCP工具定义
 * 描述 MCP Server 提供的工具的基本信息和输入要求
 */
export type McpTool = {
  /** 工具的唯一标识名称 */
  name: string;
  /** 工具的功能描述 */
  description?: string;
  /** 工具输入参数的JSON Schema定义 */
  inputSchema?: object;
};

/**
 * MCP工具调用响应类型
 * 定义了工具执行后返回的数据结构
 */
export type McpToolCallResponse = {
  _meta?: Record<string, any>;
  content: Array<
    | {
      type: 'text';
      text: string;
    }
    | {
      type: 'image';
      data: string;
      mimeType: string;
    }
    | {
      type: 'audio';
      data: string;
      mimeType: string;
    }
    | {
      type: 'resource';
      resource: {
        uri: string;
        mimeType?: string;
        text?: string;
        blob?: string;
      };
    }
  >;
  isError?: boolean;
};

/**
 * 切换MCP Server 状态参数类型
 */
export type ToggleMcpServerParams = {
  /**  MCP Server 名称 */
  serverName: string;
  /** 是否禁用 */
  disabled: boolean;
};

/**
 * 重启MCP Server 参数类型
 */
export type RestartMcpServerParams = {
  /**  MCP Server 名称 */
  serverName: string;
};

/**
 * 删除MCP Server 参数类型
 */
export type DeleteMcpServerParams = {
  /**  MCP Server 名称 */
  serverName: string;
};

/**
 * MCP Server 变更事件详情类型
 */
export type McpServerChangeEventDetail = {
  /** 是否发生错误 */
  isError: boolean;
  /** 错误代码 */
  code: number;
  /** 事件消息 */
  message: string;
  /** 当前所有MCP Server 列表 */
  mcpServers: McpServer[];
};

/**
 * MCP连接配置类型
 * 描述了一个完整的MCP连接所需的组件
 */
export type MCPConnection = {
  /**  MCP Server 配置信息 */
  server: McpServer;
  /** MCP Client实例 */
  client: Client;
  /**  MCP Server 通信传输层实例 */
  transport: StdioClientTransport | SSEClientTransport;
};

/**
 * MCP传输类型
 * 定义了支持的传输协议类型
 * - stdio: 标准输入输出传输
 * - sse: Server-Sent Events传输
 */
export type MCPTransportType = 'stdio' | 'sse';

export type MarketMcpDetail = Partial<{
  serverId: string; // mcp server id
  serverName: string;
  serverTagList: string[]; // 标签列表
  serverDescription: string; // MCP Server的描述
  serverIntroductionMarkdown: string; // 服务介绍markdown格式文本
  serverIntroductionLink: string;  // 服务介绍链接地址（当前不支持）
  provider: string; // 服务提供方
  serverIconLink: string; // mcp-server头像图标地址
  serverType: string; // MCP Server的类型，是HTTP、RPC、SSE
  usageScenarioList: string[]; // MCP Server允许的使用场景，是允许平台智能体使用，还是允许Kwaipilot使用，可以有多个
  publishAt: number; // 毫秒时间戳
  modifiedAt: number; // 修改时间，毫秒时间戳
  createdAt: number; // 创建时间，毫秒时间戳
  internalProvide: boolean; // 是否是公司内部 mcp
  serverConfig: {
    repository: {
      url: string;
      type: string;
    };
    // 对参数模版里面的占位符进行描述，是一个数组对象
    installationArguments: Array<{
      description: string;
      key: string;
      title: string;
      required: boolean;
      type: string;
      placeholder: string;
      defaultValue: string;
    }>;
    // 参数模版，用户维护的安装命令,里面是一个对象
    installationTemplate: {
      [key: string]: {
        args?: string[];
        type?: string;
        command?: string;
        url?: string;
      };
    };
  };
}>;

export type InstallMcpParams = {
  mcpServers: {
    [key: string]: {
      type?: string;
      command?: string;
      args?: string[];
      url?: string;
    };
  }
}

