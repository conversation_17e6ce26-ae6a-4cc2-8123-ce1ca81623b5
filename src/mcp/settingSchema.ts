import { unknown, z } from 'zod';

/**
 * MCP服务器默认超时时间（秒）
 * 与Anthropic MCP SDK的默认超时时间保持一致
 */
export const DEFAULT_MCP_TIMEOUT_SECONDS = 60;

/**
 * MCP服务器最小超时时间（秒）
 * 防止设置过短的超时时间导致连接问题
 */
export const MIN_MCP_TIMEOUT_SECONDS = 1;

/**
 * MCP服务器基础配置Schema
 * 定义了所有类型服务器共有的基础配置项
 */
export const BaseConfigSchema = z.object({
  /** 是否禁用该服务器 */
  disabled: z.boolean().optional(),
  /**
   * 服务器超时时间配置
   * 必须大于等于最小超时时间
   * 默认使用DEFAULT_MCP_TIMEOUT_SECONDS
   */
  timeout: z.number().min(MIN_MCP_TIMEOUT_SECONDS).optional().default(DEFAULT_MCP_TIMEOUT_SECONDS)
});

/**
 * SSE类型服务器配置Schema
 * 扩展基础配置，添加SSE特有的配置项
 */
export const SseConfigSchema = BaseConfigSchema.extend({
  /** SSE服务器URL地址 */
  url: z.string().url(),
  /** 自定义请求头 */
  headers: z.record(z.string()).optional()
}).transform((config) => ({
  ...config,
  /** 标记传输类型为SSE */
  transportType: 'sse' as const
}));

/**
 * Stdio类型服务器配置Schema
 * 扩展基础配置，添加Stdio特有的配置项
 */
export const StdioConfigSchema = BaseConfigSchema.extend({
  /** 要执行的命令 */
  command: z.string(),
  /** 命令行参数列表 */
  args: z.array(z.string()).optional(),
  /** 环境变量配置 */
  env: z.record(z.string()).optional()
}).transform((config) => ({
  ...config,
  /** 标记传输类型为Stdio */
  transportType: 'stdio' as const
}));

/**
 * 服务器配置总Schema
 * 使用union类型组合SSE和Stdio两种配置类型
 */
export const ServerConfigSchema = z.union([StdioConfigSchema, SseConfigSchema]);

/**
 * MCP设置文件Schema
 * 定义了配置文件的总体结构
 */
export const McpSettingsSchema = z.object({
  /** 服务器配置映射表，key为服务器名称 */
  mcpServers: z.record(ServerConfigSchema)
});

/**
 * 使用Zod Schema推导的MCP服务器配置类型
 */
export type McpServerConfig = z.infer<typeof ServerConfigSchema>;

/**
 * MCP设置文件Schema
 * 定义了配置文件的总体结构
 */
export const ServerConfigSimpleSchema = z.record(z.unknown());

/**
 * MCP设置文件Schema 简单版本，只校验 mcpServers 字段
 * 定义了配置文件的总体结构
 */
export const McpSettingsSimpleSchema = z.object({
  /** 服务器配置映射表，key为服务器名称 */
  mcpServers: z.record(ServerConfigSimpleSchema)
});

export type McpServerSimpleConfig = z.infer<typeof ServerConfigSimpleSchema>;
