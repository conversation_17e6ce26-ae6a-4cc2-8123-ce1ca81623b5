import i18n from 'i18n';

// 直接内联翻译数据，避免文件系统操作
const translations = {
  en: {
    "indexing.indexingFile": "Indexing: {{name}}",
    "indexing.completed": "Indexing task completed",
    "indexing.error": "The Kwaipilot plugin is open in another IDE. Indexing is temporarily unavailable. Please close the plugin, and indexing will automatically resume.",
    "indexing.startIndexing": "Starting project indexing",
    "indexing.nonGitRepoLimit": "Non-git repository indexing is currently not supported for repositories with more than 1000 files",
    "indexing.maxFilesExceeded": "{{message}}, current file count: {{count}}",
    "indexing.gitRepoError": "Git repository error, unable to fetch latest commit information",
    "indexing.gitUpdateError": "Index update error, unable to fetch latest commit information, please run git pull command",
    "agent.error": "Sorry, the request failed. Please try again later or report the issue to us."
  },
  zh: {
    "indexing.indexingFile": "索引中: {{name}} ",
    "indexing.completed": "索引任务执行完毕",
    "indexing.error": "存在其他 IDE 上打开了 Kwaipilot插件，暂时无法索引，请关闭插件后将自动继续索引",
    "indexing.startIndexing": "开始索引项目",
    "indexing.nonGitRepoLimit": "非 git 仓库，当前暂不支持文件数超过 1000 的非 git 仓库索引",
    "indexing.maxFilesExceeded": "{{message}}, 当前文件数量: {{count}}",
    "indexing.gitRepoError": "git 仓库异常，无法拉取到最新 commit信息",
    "indexing.gitUpdateError": "索引更新异常，当前无法拉取到最新commit信息，请执行 git pull 命令",
    "agent.error": "抱歉，服务请求失败，建议稍后再试或联系 @Kwaipilot 智能客服"
  }
};

// 配置 i18n，使用 staticCatalog 完全避免文件系统操作
i18n.configure({
  staticCatalog: translations,
  // objectNotation: true
});

export default i18n;