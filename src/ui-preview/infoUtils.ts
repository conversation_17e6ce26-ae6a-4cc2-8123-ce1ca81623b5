import { ISendInfo, ISendLog } from './types';
import { genElementInfo, getElementName } from './elementUtils';
import { IMessenger } from '@/protocol/messenger';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { wrapWithTag, wrapWithTagWithStack, wrapUiConsole, DISPLAYNAME_CONSOLE_ERROR } from './prompt';

/**
 * 格式化元素信息
 */
export function fmtSendInfo(data: ISendInfo): {
  type: 'element';
  displayName: string;
  data: string;
  desc?: string;
} | null {
  if (data.type !== 'element') {
    return null;
  }
  const elementInfoStr = genElementInfo(data);
  if (!elementInfoStr) {
    return null;
  }
  const displayName = getElementName(data);
  return {
    type: 'element',
    data: elementInfoStr,
    displayName: `dom-element: ${displayName}`,
    desc: displayName,
  };
}

/**
 * 发送日志信息（error/warn/log）
 */
export function sendLog(
  messenger: I<PERSON><PERSON>enger<ToCoreProtocol, FromCoreProtocol> | undefined,
  data: ISendLog[]
): void {
  if (!data || !data.length) return;
  const messages = data.map(
    (item) => {
      const tag = `${item.type || 'log'}`;
      if (item.type === 'error') {
        return wrapWithTagWithStack(tag, item.message, item.stack);
      }
      return wrapWithTag(tag, item.message);
    }
  ).join('\n');
  const desc = data.map(item => item.message).filter(Boolean);
  const dataMessage = wrapUiConsole(messages);
  messenger?.send('uiPreview/info', {
    type: 'error',
    displayName: DISPLAYNAME_CONSOLE_ERROR(desc.length),
    data: dataMessage,
    desc
  });
} 