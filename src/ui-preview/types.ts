import { z } from 'zod';

/**
 * 元素信息结构
 */
export const ISendElementSchema = z.object({
  type: z.literal('element'), // 类型标识
  url: z.string().optional(), // 当前页面的地址
  id: z.string().optional(), // 唯一 id，随机生成
  selector: z.string().optional(), // 选择器
  element: z.object({
    tag: z.string(), // 标签名
    id: z.string().optional(),
    class: z.string().optional(),
    attributes: z.record(z.any()).optional(), // 属性集合
    computedStyle: z.record(z.any()).optional(), // 计算样式
  }).optional(),
  textContent: z.string().optional(), // innerText
  ariaSnapshot: z.string().optional(),
  codeInfo: z.object({
    path: z.string().optional(), // 代码路径
    start: z.object({ line: z.number(), column: z.number() }).optional(),
    end: z.object({ line: z.number(), column: z.number() }).optional(),
  }).optional(),
  parentElement: z.object({
    tag: z.string(),
    id: z.string().optional(),
    attributes: z.record(z.any()).optional(), // 属性集合
    class: z.string().optional(),
    computedStyle: z.record(z.any()).optional(),
  }).optional(),
  fileList: z.array(z.string()).optional(), // 从当前到外的文件列表
});


export const ISendErrorLogSchema = z.object({
  type: z.literal('error'), // 日志类型
  message: z.string(), // 日志内容
  stack: z.string().optional(), // 错误堆栈信息
});

export const ISendOtherLogSchema = z.object({
  type: z.union([
    z.literal('log'),
    z.literal('warn')
  ]), // 日志类型
  message: z.string(), // 日志内容
});

/**
 * 日志/错误信息结构
 */
export const ISendLogSchema = z.union([ISendErrorLogSchema, ISendOtherLogSchema]);

/**
 * 图片信息结构
 */
export const ISendImageSchema = z.object({
  type: z.literal('image'), // 类型标识
  screenshot: z.string(), // 本地临时图片地址
});

/**
 * 统一信息结构（元素、日志、图片）
 */
export const ISendInfoSchema = z.union([
  ISendElementSchema,
  ISendLogSchema,
  ISendImageSchema,
]);

/**
 * UI Bridge 事件结构
 */
export const UIBridgeEventSchema = z.object({
  data: z.array(ISendInfoSchema), // 信息数组
});

/** 元素信息类型 */
export type ISendElement = z.infer<typeof ISendElementSchema>;

/** 错误日志信息类型 */
export type ISendErrorLog = z.infer<typeof ISendErrorLogSchema>;

/** 日志信息类型 */
export type ISendLog = z.infer<typeof ISendLogSchema>;
/** 图片信息类型 */
export type ISendImage = z.infer<typeof ISendImageSchema>;
/** 统一信息类型 */
export type ISendInfo = z.infer<typeof ISendInfoSchema>;
/** UI Bridge 事件类型 */
export type UIBridgeEvent = z.infer<typeof UIBridgeEventSchema>;

export type UIBridgeLog = {
  type: string;
  key: string;
  value: unknown;
}

export interface IBrowserManager {
  /**
   * 导航到指定URL，复用现有页面或创建新页面
   */
  navigateTo(url: string): Promise<import('playwright-core').Page>;
  /** 
   * 关闭浏览器
   */
  close(): Promise<void>;
  /**
   * 安装浏览器
   */
  installBrowser(): Promise<void>;
}