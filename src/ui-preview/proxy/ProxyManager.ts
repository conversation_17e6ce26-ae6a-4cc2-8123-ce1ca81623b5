import { PreviewProxy } from './PreviewProxy';
import { ProxyInfo } from './types';
import getPort, { portNumbers } from 'get-port';
import { resolveReachableLocalUrl } from '../portUtils';
import { LoggerManager } from '../LoggerManager';

/**
 * 内部代理数据结构
 */
interface ProxyData {
  instance: PreviewProxy;
  createdAt: Date;
  lastActiveAt: Date;
  healthCheckFailCount: number;
}

/**
 * 代理管理器 - 单例模式
 */
export class ProxyManager {
  private static instance: ProxyManager | null = null;
  private static isCreating = false; // 防止并发创建实例

  private proxies: Map<number, ProxyData> = new Map();
  private healthCheckInterval?: NodeJS.Timeout;
  private readonly MAX_HEALTH_CHECK_FAILURES = 10; // 最大连续失败次数，最大超时5分钟
  private readonly PORT_RANGE_START = 18000; // 端口范围开始
  private readonly PORT_RANGE_END = 19000; // 端口范围结束
  private readonly HEALTH_CHECK_INTERVAL = 30000; // 健康检查间隔 (30秒)

  private logger = new LoggerManager(`uiPreview-proxyManager`);
  private constructor() {
    this.logger.getUIPreviewLogger().info('[ProxyManager] instance created');
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ProxyManager {
    if (!ProxyManager.instance && !ProxyManager.isCreating) {
      ProxyManager.isCreating = true;
      ProxyManager.instance = new ProxyManager();
      ProxyManager.isCreating = false;
    }
    return ProxyManager.instance!;
  }

  /**
   * 查找可用端口
   */
  private async findAvailablePort(): Promise<number> {
    try {
      const port = await getPort({
        port: portNumbers(this.PORT_RANGE_START, this.PORT_RANGE_END)
      });

      return port;
    } catch (error) {
      throw new Error(`Failed to find available port in range ${this.PORT_RANGE_START}-${this.PORT_RANGE_END}: ${error instanceof Error ? error.message : 'Unknown error'}`); // -
    }
  }

  /**
   * 自动回收不健康的代理
   */
  private async recycleUnhealthyProxy(port: number): Promise<void> {
    this.logger.getUIPreviewLogger().warn(`[ProxyManager] Proxy on port ${port} has failed health checks ${this.MAX_HEALTH_CHECK_FAILURES} times. Recycling...`); // -

    try {
      await this.stopProxy(port);
      this.logger.getUIPreviewLogger().info(`[ProxyManager] Proxy on port ${port} recycled successfully.`);
    } catch (error) {
      this.logger.getUIPreviewLogger().error(`[ProxyManager] Failed to recycle proxy on port ${port}:`, error);
      this.proxies.delete(port);
    }
  }

  /**
   * 创建新的代理实例
   * targetUrl: 目标URL
   */
  async createProxy(targetUrl: string): Promise<ProxyInfo> {
    try {
      // 判断 ::1 / 127.0.0.1 哪个能连通
      let workingUrl = null;
      workingUrl = await resolveReachableLocalUrl(targetUrl);
      if (!workingUrl) throw new Error('Failed to find available address');
      targetUrl = workingUrl;
      // 检查是否已存在相同目标URL的代理
      for (const [port, proxyData] of this.proxies.entries()) {
        if (normalizeUrl(proxyData.instance.getTarget()) === normalizeUrl(targetUrl)) {
          this.logger.getUIPreviewLogger().info(`[ProxyManager] target ${targetUrl} already has a proxy, return existing proxy port ${port}`); // -
          // 更新最后活跃时间并重置失败计数
          proxyData.lastActiveAt = new Date();
          proxyData.healthCheckFailCount = 0; // 重置失败计数，因为有新的使用

          // 返回现有代理信息
          return {
            port,
            targetUrl,
            proxyUrl: proxyData.instance.getUrl(),
            status: proxyData.instance.getStatus(),
            createdAt: proxyData.createdAt,
            lastActiveAt: proxyData.lastActiveAt,
            isHealthy: true // 假设现有代理是健康的，不进行健康检查
          };
        }
      }

      // 如果不存在，创建新代理
      this.logger.getUIPreviewLogger().info(`[ProxyManager] create a new proxy for target ${targetUrl}`);

      // 分配可用端口
      const proxyPort = await this.findAvailablePort();
      this.logger.getUIPreviewLogger().info(`[ProxyManager] find available port ${proxyPort} for target ${targetUrl}`);
      // 创建代理实例
      const proxyInstance = new PreviewProxy(targetUrl, proxyPort);

      // 启动代理服务
      await proxyInstance.start();

      // 注册到 proxies Map（使用端口作为key）
      const now = new Date();
      this.proxies.set(proxyPort, {
        instance: proxyInstance,
        createdAt: now,
        lastActiveAt: now,
        healthCheckFailCount: 0 // 初始化失败计数
      });

      // 如果这是第一个代理，启动健康检查监控
      if (this.proxies.size === 1) {
        this.stopHealthCheckMonitoring();
        this.startHealthCheckMonitoring();
      }

      this.logger.getUIPreviewLogger().info(`[ProxyManager] proxy created successfully on port ${proxyPort} for target ${targetUrl}`); // -

      return {
        port: proxyPort,
        targetUrl,
        proxyUrl: proxyInstance.getUrl(),
        status: proxyInstance.getStatus(),
        createdAt: now,
        lastActiveAt: now,
        isHealthy: true // 新创建的代理假设是健康的，不进行健康检查
      };
    } catch (error) {
      throw new Error(`Failed to create proxy for ${targetUrl}: ${error instanceof Error ? error.message : 'Unknown error'}`); // -
    }
  }

  /**
   * 停止指定代理
   */
  async stopProxy(port: number): Promise<void> {
    const proxyData = this.proxies.get(port);
    if (!proxyData) {
      throw new Error(`Proxy on port ${port} not found`);
    }

    try {
      // 停止代理服务
      await proxyData.instance.stop();
      this.logger.getUIPreviewLogger().info(`[ProxyManager] proxy stopped successfully on port ${port}`);

      // 从 Map 中移除
      this.proxies.delete(port);

      // 如果没有代理了，停止健康检查监控
      if (this.proxies.size === 0) {
        this.stopHealthCheckMonitoring();
      }
    } catch (error) {
      throw new Error(`Failed to stop proxy on port ${port}: ${error instanceof Error ? error.message : 'Unknown error'}`); // -
    }
  }

  /**
   * 获取代理状态
   */
  async getProxyStatus(port: number): Promise<ProxyInfo | null> {
    const proxyData = this.proxies.get(port);
    if (!proxyData) {
      return null;
    }

    const { instance, createdAt, lastActiveAt } = proxyData;

    return {
      port,
      targetUrl: instance.getTarget(),
      proxyUrl: instance.getUrl(),
      status: instance.getStatus(),
      createdAt,
      lastActiveAt,
      isHealthy: await instance.healthCheck()
    };
  }

  /**
   * 获取所有代理实例
   */
  async getAllProxies(): Promise<ProxyInfo[]> {
    const proxyEntries = Array.from(this.proxies.entries());

    // 并发执行健康检查以提高性能
    const proxyPromises = proxyEntries.map(async ([port, proxyData]) => {
      const { instance, createdAt, lastActiveAt } = proxyData;

      try {
        const isHealthy = await instance.healthCheck();
        return {
          port,
          targetUrl: instance.getTarget(),
          proxyUrl: instance.getUrl(),
          status: instance.getStatus(),
          createdAt,
          lastActiveAt,
          isHealthy
        };
      } catch (error) {
        // 单个代理健康检查失败时，仍然返回基本信息，但标记为不健康
        this.logger.getUIPreviewLogger().warn(`[ProxyManager] Failed to get health status for proxy on port ${port}:`, error);
        return {
          port,
          targetUrl: instance.getTarget(),
          proxyUrl: instance.getUrl(),
          status: instance.getStatus(),
          createdAt,
          lastActiveAt,
          isHealthy: false
        };
      }
    });

    return Promise.all(proxyPromises);
  }

  /**
   * 停止所有代理实例
   */
  async stopAllProxies(): Promise<void> {
    this.logger.getUIPreviewLogger().info('[ProxyManager] Stopping all proxies...');

    const ports = Array.from(this.proxies.keys());
    const errors: string[] = [];

    for (const port of ports) {
      try {
        await this.stopProxy(port);
      } catch (error) {
        const errorMsg = `Failed to stop proxy on port ${port}: ${error instanceof Error ? error.message : 'Unknown error'}`; // -
        errors.push(errorMsg);
      }
    }

    if (errors.length > 0) {
      throw new Error(`Some proxies failed to stop: ${errors.join('; ')}`);
    }

    this.logger.getUIPreviewLogger().info('[ProxyManager] All proxies stopped successfully');
  }

  /**
   * 开始健康检查监控
   */
  private startHealthCheckMonitoring(): void {
    this.logger.getUIPreviewLogger().info('[ProxyManager] start health check monitoring');
    this.healthCheckInterval = setInterval(async () => {
      for (const [port, proxyData] of this.proxies.entries()) {
        try {
          const isHealthy = await proxyData.instance.healthCheck();
          if (isHealthy) {
            // 如果健康检查通过，更新最后活跃时间并重置失败计数
            proxyData.lastActiveAt = new Date();
            // 如果是从失败状态恢复，则强制刷新页面
            if (proxyData.healthCheckFailCount > 0) {
              this.logger.getUIPreviewLogger().info(`[ProxyManager] proxy on ${port} health recovered, refresh page`);
              proxyData.instance.refresh();
            }
            proxyData.healthCheckFailCount = 0;
          } else {
            this.logger.getUIPreviewLogger().warn(`[ProxyManager] Proxy on port ${port} health check failed`);
            proxyData.healthCheckFailCount++;

            // 检查是否需要回收
            if (proxyData.healthCheckFailCount >= this.MAX_HEALTH_CHECK_FAILURES) {
              await this.recycleUnhealthyProxy(port);
            }
          }
        } catch (error) {
          this.logger.getUIPreviewLogger().warn(`[ProxyManager] Health check error for proxy on port ${port}:`, error);
          proxyData.healthCheckFailCount++;

          // 检查是否需要回收
          if (proxyData.healthCheckFailCount >= this.MAX_HEALTH_CHECK_FAILURES) {
            await this.recycleUnhealthyProxy(port);
          }
        }
      }
    }, this.HEALTH_CHECK_INTERVAL);
  }

  /**
   * 停止健康检查监控
   */
  private stopHealthCheckMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
      this.logger.getUIPreviewLogger().info('[ProxyManager] stop health check monitoring');
    }
  }

  /**
   * 清理资源
   * 停止所有代理并清理定时器
   */
  public async dispose(): Promise<void> {
    this.logger.getUIPreviewLogger().info('[ProxyManager] Disposing ProxyManager...');

    try {
      // 停止健康检查监控 (先停止，避免在清理过程中触发)
      this.stopHealthCheckMonitoring();

      // 停止所有代理
      if (this.proxies.size > 0) {
        await this.stopAllProxies();
      }

      this.logger.getUIPreviewLogger().info('[ProxyManager] ProxyManager disposed successfully');
    } catch (error) {
      this.logger.getUIPreviewLogger().error('[ProxyManager] Error during dispose:', error);
      throw error;
    }
  }

}

function normalizeUrl(url: string): string {
  try {
    const u = new URL(url);
    let normalized = `${u.protocol}//${u.hostname}`;
    if (u.port) normalized += `:${u.port}`;
    // 保留路径但去掉结尾斜杠
    normalized += u.pathname.replace(/\/+$/, '');
    return normalized;
  } catch {
    return url;
  }
} 