import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/protocol/messenger';
import { BrowserPreview } from './browser';
import { ProxyManager } from './proxy';
import { FromCoreProtocol, ToCoreProtocol } from '@/protocol';
import { ISendElement, ISendImage, ISendInfo, ISendLog, UIBridgeLog } from './types';
import { fmtSendInfo, sendLog } from './infoUtils';
import { DISPLAYNAME_SCREENSHOT } from './prompt';
import { UIPreviewLogger } from '@/util/log';
import { LoggerManager } from './LoggerManager';
import { isPortReachable } from './portUtils';

export class UIPreviewManager {
  private static instance: UIPreviewManager;
  /** 消息通信实例 */
  private messenger?: IMessenger<ToCoreProtocol, FromCoreProtocol>;

  private logger = new LoggerManager(`uiPreview-manager`);

  private constructor() {
    // Private constructor to prevent instantiation
  }

  /**
   * 设置消息通信实例
   * 用于与核心进程进行通信
   *
   * @param messenger - 消息通信实例
   */
  public setMessenger(messenger: IM<PERSON>enger<ToCoreProtocol, FromCoreProtocol>) {
    this.messenger = messenger;
  }

  public static getInstance(): UIPreviewManager {
    if (!UIPreviewManager.instance) {
      UIPreviewManager.instance = new UIPreviewManager();
    }
    return UIPreviewManager.instance;
  }

  public async installBrowser(): Promise<void> {
    await BrowserPreview.installBrowser();
  }

  /**
   * 预览浏览器
   * @param url 
   */
  public async previewBrowser(url: string): Promise<void> {
    try {
      await BrowserPreview.navigateTo(url);
    } catch (error: any) {
      throw new Error(error?.message || 'Preview browser error');
    }
  }

  /**
   * 预览代理
   * @param url 
   */
  public async previewProxy(url: string): Promise<string> {
    try {
      const manager = ProxyManager.getInstance();
      const proxy = await manager.createProxy(url);
      return `http://localhost:${proxy.port}`;
    } catch (error: any) {
      console.error(`[UIPreviewManager] Preview proxy for ${url} error`, error);
      this.logger.getUIPreviewLogger().error(`[UIPreviewManager] Preview proxy for ${url} error`, error);
      throw new Error(error?.message || 'Preview proxy error');
    }
  }

  public async checkPortActive(url: string): Promise<boolean> {
    // 解析URL获取端口号
    const parsedUrl = new URL(url);

    // 如果URL中没有指定端口，直接返回true
    if (!parsedUrl.port) {
      return true;
    }

    // 解析端口号
    const port = parseInt(parsedUrl.port, 10);

    if (isNaN(port) || port <= 0 || port >= 65536) {
      // 如果无法检测到有效端口号，也返回true
      return true;
    }

    try {
      // 检查IPv4
      const ipv4Active = await isPortReachable('127.0.0.1', port);
      // 检查IPv6
      const ipv6Active = await isPortReachable('::1', port);
      // 只要有一个被占用就算端口被占用
      const isActive = ipv4Active || ipv6Active;
      console.log(`[UIPreviewManager] check port for ${url} ipv4/ipv6/all:`, ipv4Active, ipv6Active, isActive);
      this.logger.getUIPreviewLogger().info(`[UIPreviewManager] check port for ${url} ipv4/ipv6/all:`, ipv4Active, ipv6Active, isActive); // -
      return isActive;
    } catch (error) {
      console.error(`[UIPreviewManager] check port for ${url} error:`, error);
      this.logger.getUIPreviewLogger().error(`[UIPreviewManager] check port for ${url} error:`, error);
      return false;
    }
  }

  public async sendImage(data: ISendImage): Promise<void> {
    if (!data.screenshot || data.screenshot.trim() === '') return;
    this.messenger?.send('uiPreview/info', {
      type: data.type,
      displayName: DISPLAYNAME_SCREENSHOT,
      data: data.screenshot
    });
  }

  public sendInfo(data: ISendInfo[]): void {
    const errorData: ISendLog[] = [];
    for (const item of data) {
      if (["error", "warn", "log"].includes(item.type)) {
        errorData.push(item as ISendLog);
        continue;
      }
      const formattedData = fmtSendInfo(item);
      if (formattedData) {
        this.messenger?.send('uiPreview/info', {
          type: formattedData.type,
          displayName: formattedData.displayName,
          data: formattedData.data,
        });
      }
    }
    if (errorData.length > 0) {
      sendLog(this.messenger, errorData);
    }
  }

  // 上报用户行为埋点
  public reportUserAction(opt: UIBridgeLog) {
    try {
      let valueJSON = '';
      const { key, type, value } = opt;
      try {
        if (typeof value === 'string') {
          valueJSON = value;
        }

        if (typeof value === 'object' && value !== null) {
          valueJSON = JSON.stringify(value);
        }
      } catch (error) {
      }

      if (!key && !type) {
        return;
      }

      this.logger.reportUserAction({
        key,
        type,
        content: valueJSON
      });
    } catch (err) {
      this.logger.getUIPreviewLogger().error('reportUserAction error', err)
    }
  }

  // 通知插件刷新 in-IDE 页面
  public notifyRefresh(url: string) {
    this.messenger?.send('uiPreview/refresh', undefined);
  }
}
