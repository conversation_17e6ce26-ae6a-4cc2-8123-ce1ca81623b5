import { chromium } from 'playwright-core';
import type { SupportedBrowser } from './install';
import { GlobalConfig } from '@/util/global';
import os from 'os';
import path from 'path';

export interface BrowserConfig {
  browserType: typeof chromium;
  channel: SupportedBrowser;
  storageName: string;
}

export const browserConfig: BrowserConfig = {
  browserType: chromium,
  channel: 'chromium',
  storageName: 'chromium',
};

export const SCRIPT_URL = 'https://h2.static.yximgs.com/udata/pkg/QA-SERVEREE-FE-OUT/uiwise/injector/browser/preview-injector.min.js';
export const SCRIPT_URL_EXTERNAL = 'https://s17-def.ap4r.com/kos/s101/nlav112218/ui-dev/browser/preview-injector.min.js';

export function getPreviewScriptUrl(): string {
  const versionType = GlobalConfig.getConfig().getVersionType();

  // 只有 External 环境才使用代理路径
  const url = versionType === 'External' ? SCRIPT_URL_EXTERNAL : SCRIPT_URL;

  return url + `?t=${new Date().getTime()}`;
}

export const SCREENSHOT_DIR = path.join(os.tmpdir(), 'kwai-screenshot-images');
export const NAVIGATION_TIMEOUT = 30000;
export const MAX_CONCURRENT_SCREENSHOTS = 3; 