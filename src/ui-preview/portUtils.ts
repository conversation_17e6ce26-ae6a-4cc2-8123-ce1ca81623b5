import net from 'net';

/**
 * 检查端口是否可达
 */
export async function isPortReachable(host: string, port: number): Promise<boolean> {
    return new Promise((resolve) => {
        const socket = net.connect({ host, port, timeout: 500 }, () => {
            socket.destroy();
            resolve(true);
        });
        socket.on('error', () => resolve(false));
        socket.on('timeout', () => {
            socket.destroy();
            resolve(false);
        });
    });
}

/**
 * 尝试 fallback 到 127.0.0.1 或 ::1，如果是 localhost 的情况
 */
export async function resolveReachableLocalUrl(targetUrl: string): Promise<string | null> {
    const url = new URL(targetUrl);
    const port = parseInt(url.port || '80', 10);

    // 非 localhost，不尝试 fallback，直接返回
    if (url.hostname !== 'localhost') {
        return targetUrl;
    }

    // 是 localhost，尝试 fallback 顺序
    const fallbackHosts = ['127.0.0.1', '::1'];

    for (const host of fallbackHosts) {
        const testUrl = new URL(targetUrl);
        const reachable = await isPortReachable(host, port);
        if (reachable) {
            testUrl.hostname = host;
            return testUrl.toString();
        }
    }

    return null;
}
